import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../state/favorite_note_state.dart';

class NoteFavoriteCubit extends Cubit<NoteFavoriteState> {
  final BuyerRepository buyerRepository;

  NoteFavoriteCubit(this.buyerRepository) : super(NoteFavoriteInitial());

  Future<void> updateNote({required int id, required String note}) async {
    // final id = buyer.id!;
    emit(NoteFavoriteLoading());
    try {
      final buyerResponse =
          await buyerRepository.updateNote(id: id, note: note);
      // if (buyerResponse == null) {
      //   emit(NoteFavoriteError(Strings.commonError));
      //   return;
      // }
      emit(NoteFavoriteSuccess(buyerResponse));
    } catch (e) {
      emit(NoteFavoriteError(e.toString()));
    }
  }
}
