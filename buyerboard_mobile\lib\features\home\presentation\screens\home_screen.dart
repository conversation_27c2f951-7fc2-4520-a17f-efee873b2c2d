import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/common_utils.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/auth/presentation/cubit/social_auth_cubit.dart';
import 'package:buyer_board/features/auth/presentation/screens/auth_screen.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filter_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filtered_value_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_search_slide_animation_type_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/screens/home_search_filter_field.dart';
import 'package:buyer_board/features/filters/models/new_filter_model.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_state.dart';
import 'package:buyer_board/features/home/<USER>/screens/location_required_screen.dart';
import 'package:buyer_board/features/home/<USER>/widgets/buyer_card_item.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
  }

  void onLogout() {
    context.read<SocialAuthCubit>().logOut();
    authActionNotifier.value = AuthAction.none;
    context.go(PagePath.authScreen);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => CommonUtils.hideKeyboard(),
      child: Scaffold(
        appBar: AppBar(
          iconTheme: const IconThemeData(color: AppColors.white),
          title: const Text(
            Strings.buyerBoard,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.menu),
            color: AppColors.white,
            onPressed: () {
              context.push(PagePath.menu);
            },
          ),
          actions: [
            BlocBuilder<NewFilterCubit, NewFilterModel>(
              builder: (_, data) {
                final isFiltering = data.filters?.isFiltering ?? false;
                return IconButton(
                  onPressed: () => context.push(PagePath.filterScreen),
                  icon: Stack(
                    children: [
                      const Icon(Icons.filter_alt_outlined),
                      if (isFiltering)
                        const Positioned(
                          right: 0,
                          top: 0,
                          child: CircleAvatar(
                            radius: 4,
                            backgroundColor: Colors.red,
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
        body: Column(
          children: [
            const HomeSearchFilterField(),
            LocationRequiredScreen(
              child: BlocBuilder<BuyersBloc, BuyersState>(
                builder: (_, state) {
                  return switch (state) {
                    BuyersLoading() => Expanded(child: _loader()),
                    BuyersLoaded(buyers: var buyers) => _BuyersListing(
                        buyers,
                        hasReachedMax: state.hasReachedMax || buyers.length < 5,
                      ),
                    BuyersError(error: var _) => const SizedBox.shrink(),
                  };
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _loader() => const Center(
        child: CupertinoActivityIndicator(
          radius: 16,
          color: AppColors.primary,
        ),
      );
}

class _BuyersListing extends StatefulWidget {
  const _BuyersListing(
    this.buyers, {
    this.hasReachedMax = false,
  });

  final List<BuyerModel> buyers;
  final bool hasReachedMax;
  @override
  State<_BuyersListing> createState() => _BuyersListingState();
}

class _BuyersListingState extends State<_BuyersListing> {
  void applySearch(
    List<LocationEntity> locations,
    DismissDirection direction,
    BuildContext context,
  ) {
    final locationState = context.read<CurrentLocationCubit>().state;
    LocationEntity? filteredLocation;
    int filteredIndex = -1;
    final length = locations.length;

    if (locationState is LocationDataState) {
      filteredLocation = locationState.location;
      if (filteredLocation != null) {
        filteredIndex = locations.indexWhere(
          (element) => element.zipCode == filteredLocation!.zipCode,
        );
      }
    }
    filteredIndex = (filteredIndex == -1) ? 0 : filteredIndex;
    late final LocationEntity selectedLocation;
    if (direction == DismissDirection.endToStart) {
      if (filteredIndex == length - 1) {
        selectedLocation = locations[0];
      } else {
        selectedLocation = locations[filteredIndex + 1];
      }
    } else if (direction == DismissDirection.startToEnd) {
      if (filteredIndex == 0) {
        selectedLocation = locations[length - 1];
      } else {
        selectedLocation = locations[filteredIndex - 1];
      }
    }

    context.read<BuyerSearchSlideAnimationTypeCubit>().changeType(
          context: context,
          newLocation: selectedLocation,
          oldLocation: filteredLocation ?? selectedLocation,
        );
    context.read<CurrentLocationCubit>().update(selectedLocation);
  }

  final ScrollController _scrollController = ScrollController();

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      context.read<BuyersBloc>().add(LoadBuyers(context: context));
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: BlocBuilder<BuyerFilterLocationsCubit, BuyerLocationFilterState>(
        builder: (context, state) {
          return switch (state) {
            BuyerLocationFilterError(message: String error) => Text(error),
            BuyerLocationFilterLoaded(locations: var locations) => Dismissible(
                key: const Key('dismissible'),
                onDismissed: (direction) {
                  applySearch(locations, direction, context);
                },
                child: widget.buyers.isEmpty
                    ? Container(
                        alignment: Alignment.center,
                        height: MediaQuery.sizeOf(context).height * 0.7,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'No BuyerCards...',
                              style: AppStyles.mediumBold.copyWith(
                                  color: context.colorScheme.onSurfaceVariant,
                                  fontSize: 20),
                            ),
                            Text(
                              'There are no buyers in this area',
                              style: AppStyles.mediumBold.copyWith(
                                color: context.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: [
                          Expanded(
                            child: ListView.separated(
                              controller: _scrollController
                                ..addListener(_onScroll),
                              padding:
                                  const EdgeInsets.all(Dimensions.padding_8),
                              itemBuilder: (ctx, index) {
                                if (index == widget.buyers.length) {
                                  return widget.hasReachedMax
                                      ? const SizedBox.shrink()
                                      : Center(
                                          child: CupertinoActivityIndicator(
                                            radius: 16,
                                            color: context.appColors.pPXLight,
                                          ),
                                        );
                                }
                                final buyer = widget.buyers[index];
                                return BuyerCardItem(buyer: buyer);
                              },
                              separatorBuilder: (ctx, index) =>
                                  const SizedBox(height: 8),
                              itemCount: widget.buyers.length + 1,
                            ),
                          ),
                        ],
                      ),
              ),
            _ => const CupertinoActivityIndicator(
                radius: 16,
                color: AppColors.primary,
              ),
          };
        },
      ),
    );
  }
}
