import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/common/widgets/profile_text_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/settings/presentation/widgets/setting_section_base_widget.dart';
import 'package:flutter/material.dart';

class UpdateLoginCredentialsScreen extends StatelessWidget {
  const UpdateLoginCredentialsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Column(
          children: [
            Text(Strings.updateLoginCredentials),
            Text(
              Strings.menuSettings,
              style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w200),
            ),
          ],
        ),
        automaticallyImplyLeading: false,
        actions: [
          TextButton(
            child: Text(
              'Done',
              style: AppStyles.medium.copyWith(
                color: AppColors.white,
              ),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const _UpdateEmailPasswordSection(),
            spacerH24,
            const _AlternateLoginMethodSection(),
            Divider(
              height: 52,
              color: context.colorScheme.outlineVariant,
            ),
            const _ActionsSection(),
          ],
        ),
      ),
    );
  }
}

class _UpdateEmailPasswordSection extends StatelessWidget {
  const _UpdateEmailPasswordSection();

  @override
  Widget build(BuildContext context) {
    return SettingSectionBaseWidget(
      title: Strings.emailAndPassword,
      subtitle: Strings.emailPasswordSubtitle,
      child: Column(
        children: [
          spacerH8,
          ProfileTextField(
            controller: TextEditingController(),
            hint: Strings.email,
            label: Strings.emailAddress,
            keyboardType: TextInputType.emailAddress,
          ),
          spacerH12,
          ProfileTextField(
            controller: TextEditingController(),
            hint: Strings.password,
            label: Strings.password,
            keyboardType: TextInputType.visiblePassword,
          ),
        ],
      ),
    );
  }
}

class _AlternateLoginMethodSection extends StatelessWidget {
  const _AlternateLoginMethodSection();

  @override
  Widget build(BuildContext context) {
    return SettingSectionBaseWidget(
      title: Strings.alternateLoginMethod,
      subtitle: Strings.alternateLoginMethodSubtitle,
      child: Column(
        children: [
          spacerH8,
          CommonButton.loginWithApple(
            action: () {},
            isOutlined: true,
            textColor: context.colorScheme.primary,
          ),
          CommonButton.loginWithGoogle(
            action: () {},
            textColor: context.colorScheme.primary,
            isOutlined: true,
          ),
        ],
      ),
    );
  }
}

class _ActionsSection extends StatelessWidget {
  const _ActionsSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        spacerH8,
        CommonButton(
          label: Strings.save,
          action: () {},
          backgroundColor: context.colorScheme.primaryContainer,
          textColor: context.colorScheme.onPrimaryContainer,
        ),
        spacerH4,
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {},
            child: const Text(Strings.cancel),
          ),
        ),
        spacerH4,
        CommonButton(
          label: Strings.delete,
          backgroundColor: Colors.transparent,
          textColor: context.colorScheme.error,
          action: () {},
        ),
      ],
    );
  }
}
