// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'new_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NewFilterModel _$NewFilterModelFromJson(Map<String, dynamic> json) =>
    NewFilterModel(
      filters: json['filters'] == null
          ? null
          : FilterOption.fromJson(json['filters'] as Map<String, dynamic>),
      sortOptions: json['sort_options'] == null
          ? null
          : FilterSortOption.fromJson(
              json['sort_options'] as Map<String, dynamic>),
      page: (json['page'] as num?)?.toInt(),
      pageSize: (json['page_size'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NewFilterModelToJson(NewFilterModel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('filters', instance.filters?.toJson());
  writeNotNull('sort_options', instance.sortOptions?.toJson());
  writeNotNull('page', instance.page);
  writeNotNull('page_size', instance.pageSize);
  return val;
}

FilterOption _$FilterOptionFromJson(Map<String, dynamic> json) => FilterOption(
      financialStatus: (json['financial_status'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$FinancialStatusEnumMap, e))
          .toList(),
      minArea: (json['min_area'] as num?)?.toDouble(),
      minBathrooms: (json['min_bathrooms'] as num?)?.toDouble(),
      minBedrooms: (json['min_bedrooms'] as num?)?.toDouble(),
      propertyType: (json['property_type'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$PropertyTypeEnumMap, e))
          .toList(),
      purchaseType:
          $enumDecodeNullable(_$PurchaseTypeEnumMap, json['purchase_type']),
      searchZipCode: json['search_zip_code'] as String?,
      hideMyBuyers: json['hide_my_buyers'] as bool? ?? false,
    );

Map<String, dynamic> _$FilterOptionToJson(FilterOption instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull(
      'financial_status',
      instance.financialStatus
          ?.map((e) => _$FinancialStatusEnumMap[e]!)
          .toList());
  writeNotNull('min_area', instance.minArea);
  writeNotNull('min_bathrooms', instance.minBathrooms);
  writeNotNull('min_bedrooms', instance.minBedrooms);
  writeNotNull('property_type',
      instance.propertyType?.map((e) => _$PropertyTypeEnumMap[e]!).toList());
  writeNotNull('purchase_type', _$PurchaseTypeEnumMap[instance.purchaseType]);
  writeNotNull('search_zip_code', instance.searchZipCode);
  val['hide_my_buyers'] = instance.hideMyBuyers;
  return val;
}

const _$FinancialStatusEnumMap = {
  FinancialStatus.preQualified: 'pre_qualified',
  FinancialStatus.preApproved: 'pre_approved',
  FinancialStatus.allCash: 'all_cash',
  FinancialStatus.nA: 'n_a',
};

const _$PropertyTypeEnumMap = {
  PropertyType.singleFamily: 'single_family_house',
  PropertyType.townhouse: 'townhouse',
  PropertyType.condo: 'condo',
  PropertyType.apartment: 'apartment',
  PropertyType.multiFamily: 'multi_family_house',
  PropertyType.land: 'land',
};

const _$PurchaseTypeEnumMap = {
  PurchaseType.purchase: 'purchase',
  PurchaseType.lease: 'lease',
};

FilterSortOption _$FilterSortOptionFromJson(Map<String, dynamic> json) =>
    FilterSortOption(
      buyerLocationsOfInterest: $enumDecode(
          _$FilterSortOrderEnumMap, json['buyer_locations_of_interest']),
      insertedAt: $enumDecode(_$FilterSortOrderEnumMap, json['inserted_at']),
    );

Map<String, dynamic> _$FilterSortOptionToJson(FilterSortOption instance) =>
    <String, dynamic>{
      'buyer_locations_of_interest':
          _$FilterSortOrderEnumMap[instance.buyerLocationsOfInterest]!,
      'inserted_at': _$FilterSortOrderEnumMap[instance.insertedAt]!,
    };

const _$FilterSortOrderEnumMap = {
  FilterSortOrder.asc: 'asc',
  FilterSortOrder.desc: 'desc',
};
