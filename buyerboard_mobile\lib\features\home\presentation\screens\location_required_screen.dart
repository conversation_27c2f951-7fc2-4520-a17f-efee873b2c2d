import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filtered_value_cubit.dart';
import 'package:buyer_board/features/home/<USER>/cubit/home_tab_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class LocationRequiredScreen extends StatelessWidget {
  const LocationRequiredScreen({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CurrentLocationCubit, LocationState>(
      builder: (context, state) {
        return switch (state) {
          LocationLoadingState() => const SizedBox(),
          LocationDataState(location: LocationEntity? location) =>
            _LocationRequiredBody(location: location, child: child),
        };
      },
    );
  }
}

class _LocationRequiredBody extends StatelessWidget {
  const _LocationRequiredBody({
    super.key,
    required this.location,
    required this.child,
  });

  final LocationEntity? location;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final typography = context.typography;
    final noLocation = location == null || location?.zipCode.isEmpty == true;

    return !noLocation
        ? child
        : Column(
            children: [
              spacerH32,
              Text(
                'No BuyerCards...',
                style: typography.largeSemi.copyWith(
                  color: appColors.greyMGreyL,
                ),
              ),
              spacerH20,
              _ActionRow(
                instruction: 'Turn on share location in ',
                actionText: 'Settings',
                onAction: () {
                  context.pushNamed(PagePath.settingsScreen);
                },
              ),
              Text(
                '-or-',
                style: typography.mediumReg.copyWith(
                  color: appColors.greyMGreyL,
                ),
              ),
              _ActionRow(
                instruction: 'Provide location in ',
                actionText: 'User Profile',
                onAction: () {
                  context.read<HomeBottomNarBarTabCubit>().changeTab(4);
                },
              ),
            ],
          );
  }
}

class _ActionRow extends StatelessWidget {
  const _ActionRow({
    super.key,
    required this.instruction,
    required this.actionText,
    required this.onAction,
  });

  final String instruction;
  final String actionText;
  final VoidCallback onAction;

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final typography = context.typography;
    return RichText(
      text: TextSpan(
        text: instruction,
        style: typography.mediumReg.copyWith(
          color: appColors.greyMGreyL,
        ),
        children: [
          TextSpan(
            recognizer: TapGestureRecognizer()..onTap = onAction,
            text: actionText,
            style: typography.mediumSemi.copyWith(
              color: appColors.pPXLight,
            ),
          ),
        ],
      ),
    );
  }
}
