import 'package:buyer_board/core/resources/colors.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

abstract class AppStyles {
  //5X Large
  static TextStyle large5x = GoogleFonts.notoSans(
      fontSize: 61, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle largeSemiBold5x =
      large5x.copyWith(fontWeight: FontWeight.w600);
  static TextStyle largeBold5x = large5x.copyWith(fontWeight: FontWeight.w900);
  //4X Large
  static TextStyle large4x = GoogleFonts.notoSans(
      fontSize: 49, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle largeSemiBold4x =
      large4x.copyWith(fontWeight: FontWeight.w600);
  static TextStyle largeBold4x = large4x.copyWith(fontWeight: FontWeight.w900);
  //3X Large
  static TextStyle large3x = GoogleFonts.notoSans(
      fontSize: 39, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle largeSemiBold3x =
      large3x.copyWith(fontWeight: FontWeight.w600);
  static TextStyle largeBold3x = large3x.copyWith(
    fontWeight: FontWeight.w900,
  );
  //2X Large
  static TextStyle large2x = GoogleFonts.notoSans(
      fontSize: 31, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle largeSemiBold2x =
      large2x.copyWith(fontWeight: FontWeight.w600);
  static TextStyle largeBold2x = large2x.copyWith(fontWeight: FontWeight.w900);
  //Large
  static TextStyle large = GoogleFonts.notoSans(
      fontSize: 25, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle largeSemiBold = large.copyWith(fontWeight: FontWeight.w600);
  static TextStyle largeBold = large.copyWith(fontWeight: FontWeight.bold);
  //Medium
  static TextStyle medium = GoogleFonts.notoSans(
      fontSize: 16, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle mediumSemiBold =
      medium.copyWith(fontWeight: FontWeight.w600);
  static TextStyle mediumBold = medium.copyWith(fontWeight: FontWeight.w900);
  //Small
  static TextStyle small = GoogleFonts.notoSans(
      fontSize: 13, color: AppColors.black, fontWeight: FontWeight.w400);
  static TextStyle smallSemiBold = small.copyWith(fontWeight: FontWeight.w600);
  static TextStyle smallBold = small.copyWith(fontWeight: FontWeight.w900);

  //Section Heading
  static TextStyle sectionHeading = largeBold;
  static TextStyle sectionSubHeading = medium;
}
