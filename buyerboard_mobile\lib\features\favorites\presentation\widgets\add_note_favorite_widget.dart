import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/drawables.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/note_favorite_cubit.dart';
import 'package:buyer_board/features/favorites/presentation/state/favorite_note_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class AddNoteFavoriteWidget extends StatefulWidget {
  const AddNoteFavoriteWidget({
    super.key,
    required this.buyer,
  });

  final BuyerModel buyer;

  @override
  State<AddNoteFavoriteWidget> createState() => _AddNoteFavoriteWidgetState();
}

class _AddNoteFavoriteWidgetState extends State<AddNoteFavoriteWidget> {
  final _noteController = TextEditingController();

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hasNote =
        widget.buyer.notes != null && widget.buyer.notes!.isNotEmpty;
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (hasNote) ...[
          Expanded(
            child: Text(
              widget.buyer.notes!,
              style: AppStyles.small.copyWith(
                color: context.colorScheme.onSurface,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: () => _showNoteDialog(context, hasNote),
            style: IconButton.styleFrom(
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            icon: SvgPicture.asset(
              Drawables.textInputIcon,
              colorFilter: ColorFilter.mode(
                context.colorScheme.onSurfaceVariant,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
        if (!hasNote)
          Directionality(
            textDirection: TextDirection.rtl,
            child: TextButton.icon(
              style: TextButton.styleFrom(
                foregroundColor: context.colorScheme.primary,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () => _showNoteDialog(context, hasNote),
              label: const Text(Strings.addANote),
              icon: const Icon(Icons.add),
            ),
          ),
      ],
    );
  }

  Future<void> _updateNote() async {
    if (_noteController.text.isEmpty || widget.buyer.id == null) {
      return;
    }
    await context.read<NoteFavoriteCubit>().updateNote(
          id: widget.buyer.id!,
          note: _noteController.text,
        );

    if (mounted) {
      context.read<FavouriteBuyersCubit>().getFavouriteBuyers(silent: true);
      context.pop();
    }
  }

  void _showNoteDialog(BuildContext context, bool isEdit) {
    final colors = context.colorScheme;
    final note = widget.buyer.notes ?? '';
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          buttonPadding: EdgeInsets.zero,
          backgroundColor: colors.primary,
          titlePadding: const EdgeInsets.all(16),
          contentPadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Container(
            width: double.infinity,
            alignment: Alignment.center,
            child: Text(
              isEdit ? Strings.editNote : Strings.addANote,
              style: AppStyles.mediumSemiBold.copyWith(
                color: colors.onPrimary,
              ),
            ),
          ),
          content: SizedBox(
            width: MediaQuery.sizeOf(context).width,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextField(
                    maxLines: 3,
                    maxLength: 100,
                    autofocus: true,
                    buildCounter: (
                      context, {
                      required currentLength,
                      required isFocused,
                      required maxLength,
                    }) {
                      return Text(
                        '$currentLength / $maxLength',
                        style: AppStyles.small.copyWith(
                          color: colors.surface,
                          fontSize: 10,
                        ),
                      );
                    },
                    style: AppStyles.small.copyWith(
                      color: colors.surface,
                    ),
                    controller: _noteController..text = note,
                    cursorColor: colors.onPrimary,
                    textCapitalization: TextCapitalization.sentences,
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(RegExp(r'^\s+')),
                    ],
                    decoration: InputDecoration(
                      fillColor: colors.inversePrimary,
                      filled: true,
                      hintText: Strings.enterYourNoteHere,
                      hintStyle: AppStyles.small.copyWith(
                        color: colors.surface,
                      ),
                      border: OutlineInputBorder(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(8)),
                        borderSide: BorderSide(
                          color: colors.surface,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(8)),
                        borderSide: BorderSide(
                          color: colors.surface,
                        ),
                      ),
                    ),
                  ),
                ),
                spacerH12,
                const Divider(height: 0),
                IntrinsicHeight(
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => context.pop(),
                          style: TextButton.styleFrom(
                            foregroundColor: colors.onPrimary,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadiusDirectional.only(
                                bottomStart: Radius.circular(15),
                              ),
                            ),
                          ),
                          child: const Text(Strings.cancel),
                        ),
                      ),
                      const VerticalDivider(width: 0, thickness: 1),
                      Expanded(
                        child:
                            BlocBuilder<NoteFavoriteCubit, NoteFavoriteState>(
                          builder: (context, state) {
                            return switch (state) {
                              NoteFavoriteLoading() => UnconstrainedBox(
                                  child: SizedBox(
                                    height: 30,
                                    width: 30,
                                    child: CircularProgressIndicator.adaptive(
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                      colors.onPrimary,
                                    )),
                                  ),
                                ),
                              _ => TextButton(
                                  style: TextButton.styleFrom(
                                    foregroundColor: colors.onPrimary,
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    shape: const RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadiusDirectional.only(
                                        bottomEnd: Radius.circular(15),
                                      ),
                                    ),
                                  ),
                                  onPressed: _updateNote,
                                  child: Text(
                                      isEdit ? Strings.update : Strings.save),
                                )
                            };
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
