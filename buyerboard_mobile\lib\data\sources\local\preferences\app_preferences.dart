import 'package:buyer_board/features/auth/data/models/requests/auth_request.dart';
import 'package:flutter/material.dart';
import '../../../../core/persistence/preferences/base_preferences.dart';
import '../../../../features/auth/data/models/response/auth_response.dart';

class AppPreferences extends BasePreferences {
  final String _userKey = "USER";
  final String _userEmailKey = "USER_EMAIL";
  final String _userPasswordKey = "USER_PASSWORD";
  final String _userOnboardingStatusKey = "USER_ONBOARDED";

  bool isFreshInstall() {
    final userOnboarded = retrieve<bool>(_userOnboardingStatusKey) ?? false;
    print("User Onboarding Status: $userOnboarded");
    print("Fresh Install: ${!userOnboarded}");
    return !userOnboarded;
  }

  void setupUserOnboardedStatus() {
    store(_userOnboardingStatusKey, true);
    final userOnboarded = retrieve<bool>(_userOnboardingStatusKey) ?? false;
    print("User Onboarding Status: $userOnboarded");
    print("Fresh Install: ${!userOnboarded}");
  }

  void setUser(User user) =>
      store<Map<String, dynamic>>(_userKey, user.toJson());
  User? getUser() {
    final user = retrieve<Map<String, dynamic>>(_userKey);
    return user != null ? User.fromJson(user) : null;
  }

  void saveUserCredentials(
      {required String email, required String password}) async {
    await storeSecure(_userEmailKey, email);
    await storeSecure(_userPasswordKey, password);
    debugPrint("User Credentials Stored for Email: $email");
  }

  void clearUserData() async {
    remove(_userKey);
    remove(_userEmailKey);
    remove(_userPasswordKey);
    debugPrint("User Credentials Cleared");
  }

  Future<AuthRequest?> getUserCredentials() async {
    final email = await retrieveSecure(_userEmailKey);
    final password = await retrieveSecure(_userPasswordKey);
    return email != null && password != null
        ? AuthRequest(email: email, password: password)
        : null;
  }

  void clearAppPreferences() async {
    await removeAllSecure();
    remove(_userKey);
    remove(_userEmailKey);
    remove(_userPasswordKey);
    remove(_userOnboardingStatusKey);
  }
}
