import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
part 'chat_message.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class ChatMessage extends Equatable {
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '', name: 'content')
  final String message;
  @JsonKey(includeFromJson: false, defaultValue: false)
  final bool isSender;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'initiated_at')
  final String timestamp;
  final int? sentBy;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: ChatMessageStatus.sent)
  final ChatMessageStatus status;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: false)
  final bool? isDeleted;
  final int? parentId;
  final ChatMessage? parentMessage;
  @JsonKey(defaultValue: [])
  final List<ChatMessageAttachment> attachments;

  const ChatMessage({
    this.id,
    required this.message,
    required this.sentBy,
    required this.timestamp,
    required this.status,
    this.isDeleted = false,
    this.parentId,
    this.parentMessage,
    this.attachments = const [],
  }) : isSender = sentBy == null;

  ChatMessage copyWith({
    int? id,
    String? message,
    String? timestamp,
    ChatMessageStatus? status,
    int? sentBy,
    bool? isDeleted,
    int? parentId,
    ChatMessage? parentMessage,
    List<ChatMessageAttachment>? attachments,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      sentBy: sentBy ?? this.sentBy,
      isDeleted: isDeleted ?? this.isDeleted,
      parentId: parentId ?? this.parentId,
      parentMessage: parentMessage ?? this.parentMessage,
      attachments: attachments ?? this.attachments,
    );
  }

  @override
  List<Object?> get props =>
      [id, message, isSender, timestamp, status, parentId, attachments];

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class ChatMessageAttachment {
  final String type;
  final String url;
  dynamic thumbnailUrl;

  ChatMessageAttachment({
    required this.type,
    required this.url,
    this.thumbnailUrl,
  });

  factory ChatMessageAttachment.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageAttachmentFromJson(json);

  Map<String, dynamic> toJson() => _$ChatMessageAttachmentToJson(this);
}

enum ChatMessageStatus { sending, sent, failed }
