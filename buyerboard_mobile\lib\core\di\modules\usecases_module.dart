import 'package:buyer_board/features/buyers_location_filter/domain/usecases/get_filter_locations.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/usecases/get_search_locations.dart';
import 'package:kiwi/kiwi.dart';

abstract final class UseCasesInjection {
  static late KiwiContainer _container;

  static void setup(KiwiContainer container) {
    _container = container;
    _setupGetFilterLocationsUseCase();
    _registerSearchLocationsByZipCodeUseCase();
  }

  static void _setupGetFilterLocationsUseCase() {
    _container.registerSingleton<GetFilterLocationsUseCase>(
      (_) => GetFilterLocationsUseCase(
        _container.resolve(),
      ),
    );
  }

  static void _registerSearchLocationsByZipCodeUseCase() {
    _container.registerSingleton<SearchLocationsByZipCodeUseCase>(
      (_) => SearchLocationsByZipCodeUseCase(
        _container.resolve(),
      ),
    );
  }
}
