import 'package:buyer_board/common/models/wrapper.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'new_filter_model.g.dart';

@JsonSerializable(
    fieldRename: FieldRename.snake, includeIfNull: false, explicitToJson: true)
class NewFilterModel {
  NewFilterModel({
    required this.filters,
    required this.sortOptions,
    required this.page,
    required this.pageSize,
  });

  final FilterOption? filters;
  final FilterSortOption? sortOptions;
  // "page":"1",
  // "page_size":"10"
  final int? page;
  final int? pageSize;

  factory NewFilterModel.fromJson(Map<String, dynamic> json) =>
      _$NewFilterModelFromJson(json);

  Map<String, dynamic> toJson() => _$NewFilterModelToJson(this);

  NewFilterModel copyWith({
    Wrapped<FilterOption?>? filters,
    Wrapped<FilterSortOption?>? sortOptions,
    Wrapped<int?>? page,
    Wrapped<int?>? pageSize,
  }) {
    return NewFilterModel(
      // filters: filters?.value ?? this.filters,
      // sortOptions: sortOptions?.value ?? this.sortOptions,
      filters: filters != null ? filters.value : this.filters,
      sortOptions: sortOptions != null ? sortOptions.value : this.sortOptions,
      page: page != null ? page.value : this.page,
      pageSize: pageSize != null ? pageSize.value : this.pageSize,
    );
  }
}

@JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
class FilterOption {
  FilterOption({
    this.financialStatus,
    this.minArea,
    this.minBathrooms,
    this.minBedrooms,
    this.propertyType,
    this.purchaseType,
    this.searchZipCode,
    this.hideMyBuyers = false,
  });

  final List<FinancialStatus>? financialStatus;
  final double? minArea;
  final double? minBathrooms;
  final double? minBedrooms;
  final List<PropertyType>? propertyType;
  final PurchaseType? purchaseType;
  final String? searchZipCode;
  final bool hideMyBuyers;

  factory FilterOption.fromJson(Map<String, dynamic> json) =>
      _$FilterOptionFromJson(json);

  Map<String, dynamic> toJson() => _$FilterOptionToJson(this);

  bool get isFiltering {
    return financialStatus != null && financialStatus!.isNotEmpty ||
        (minArea != null || minBathrooms != null || minBedrooms != null) ||
        (propertyType != null && propertyType!.isNotEmpty) ||
        purchaseType != null ||
        hideMyBuyers;
  }

  FilterOption copyWith({
    // List<FinancialStatus>? financialStatus,
    // double? minArea,
    // double? minBathrooms,
    // double? minBedrooms,
    // List<PropertyType>? propertyType,
    // PurchaseType? purchaseType,
    // String? searchZipCode,
    Wrapped<List<FinancialStatus>?>? financialStatus,
    Wrapped<double?>? minArea,
    Wrapped<double?>? minBathrooms,
    Wrapped<double?>? minBedrooms,
    Wrapped<List<PropertyType>?>? propertyType,
    Wrapped<PurchaseType?>? purchaseType,
    Wrapped<String?>? searchZipCode,
    Wrapped<bool>? hideMyBuyers,
  }) {
    return FilterOption(
      financialStatus: financialStatus != null
          ? financialStatus.value
          : this.financialStatus,
      minArea: minArea != null ? minArea.value : this.minArea,
      minBathrooms:
          minBathrooms != null ? minBathrooms.value : this.minBathrooms,
      minBedrooms: minBedrooms != null ? minBedrooms.value : this.minBedrooms,
      propertyType:
          propertyType != null ? propertyType.value : this.propertyType,
      purchaseType:
          purchaseType != null ? purchaseType.value : this.purchaseType,
      searchZipCode:
          searchZipCode != null ? searchZipCode.value : this.searchZipCode,
      hideMyBuyers:
          hideMyBuyers != null ? hideMyBuyers.value : this.hideMyBuyers,
    );
  }
}

@JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
class FilterSortOption {
  FilterSortOption({
    required this.buyerLocationsOfInterest,
    required this.insertedAt,
  });

  final FilterSortOrder buyerLocationsOfInterest;
  final FilterSortOrder insertedAt;

  factory FilterSortOption.fromJson(Map<String, dynamic> json) =>
      _$FilterSortOptionFromJson(json);

  Map<String, dynamic> toJson() => _$FilterSortOptionToJson(this);

  FilterSortOption copyWith({
    // FilterSortOrder? buyerLocationsOfInterest,
    // FilterSortOrder? insertedAt,
    Wrapped<FilterSortOrder>? buyerLocationsOfInterest,
    Wrapped<FilterSortOrder>? insertedAt,
  }) {
    return FilterSortOption(
      // buyerLocationsOfInterest:
      //     buyerLocationsOfInterest?.value ?? this.buyerLocationsOfInterest,
      // insertedAt: insertedAt?.value ?? this.insertedAt,
      buyerLocationsOfInterest: buyerLocationsOfInterest != null
          ? buyerLocationsOfInterest.value
          : this.buyerLocationsOfInterest,
      insertedAt: insertedAt != null ? insertedAt.value : this.insertedAt,
    );
  }
}

enum FilterSortOrder {
  @JsonValue('asc')
  asc,
  @JsonValue('desc')
  desc
}
