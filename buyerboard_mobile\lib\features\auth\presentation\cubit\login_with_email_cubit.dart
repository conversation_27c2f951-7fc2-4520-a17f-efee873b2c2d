import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/utils/local_auth_util.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/auth/data/models/requests/auth_request.dart';
import 'package:buyer_board/features/auth/domain/repository/auth_repository.dart';
import 'package:buyer_board/features/auth/presentation/state/login_with_email_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/resources/resources.dart';

class LoginWithEmailCubit extends Cubit<LoginWithEmailState> {
  LoginWithEmailCubit(
      {required this.authRepository,
      required this.appPreferences,
      required this.localAuth})
      : super(const LoginWithEmailState.initial());
  final AuthRepository authRepository;
  final AppPreferences appPreferences;
  final LocalAuthUtil localAuth;

  void runBiometricAuth() async {
    try {
      final auth = await localAuth.authenticate();
      if (auth) {
        final userCredentials = await appPreferences.getUserCredentials();
        userCredentials != null
            ? emit(LoginWithEmailState.localAuthSuccess(userCredentials))
            : emit(const LoginWithEmailState.loginError(Strings.commonError));
      }
    } catch (e) {
      debugPrint(e.toString());
      emit(LoginWithEmailState.loginError(e.toString()));
    }
  }

  String _getNavRoute() {
    final isFreshInstall = appPreferences.isFreshInstall();
    return isFreshInstall ? PagePath.onboardingScreen : PagePath.mainScreen;
  }

  Future<void> loginWithEmailAndPassword(BuildContext context,
      {required String email,
      required String password,
      required bool rememberMe}) async {
    emit(const LoginWithEmailState.loading());
    try {
      final response = await authRepository.loginWithEmailPassword(
        loginWithEmailPasswordRequest:
            AuthRequest(email: email, password: password),
      );
      const route = PagePath.mainScreen;
      if (response.data != null) {
        rememberMe
            ? {
                appPreferences.setUser(response.data!),
                emit(LoginWithEmailState.rememberMe(
                    response.message.body ?? '', route)),
              }
            : {
                // ignore: use_build_context_synchronously
                context.read<UserSessionCubit>().setUser(response.data!),
                emit(LoginWithEmailState.success(
                    response.data!, response.message.body ?? '', route))
              };
      }
    } catch (e) {
      debugPrint(e.toString());
      emit(LoginWithEmailState.loginError(
        e.toString(),
      ));
    }
  }
}
