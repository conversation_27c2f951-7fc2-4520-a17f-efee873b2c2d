import 'package:buyer_board/features/add_buyer/data/models/buyer_info.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum TimeLine { none, asap, three_months, six_months, open }

final ValueNotifier<PurchaseType?> purchaseTypeNotifier =
    ValueNotifier<PurchaseType?>(null);
final ValueNotifier<PropertyType?> propertyTypeNotifier =
    ValueNotifier<PropertyType?>(null);
final ValueNotifier<FinancialStatus?> financialStatusNotifier =
    ValueNotifier<FinancialStatus?>(null);
final ValueNotifier<TimeLine> timelineNotifier =
    ValueNotifier<TimeLine>(TimeLine.none);
final ValueNotifier<String?> financialStatusError = ValueNotifier<String?>("");
final ValueNotifier<String?> propertyTypeError = ValueNotifier<String?>("");
final ValueNotifier<String?> purchaseTypeError = ValueNotifier<String?>("");

class BuyerInfoCubit extends Cubit<BuyerInfo> {
  BuyerInfoCubit() : super(BuyerInfo.initial());

  void syncBuyerInfo(BuyerModel buyerModel) =>
      emit(BuyerInfo.fromBuyerModel(buyerModel));
  void setBuyerFirstName(String? firstName) {
    emit(state.copyWith(firstName: firstName));
  }

  void setBuyerLastName(String? lastName) {
    emit(state.copyWith(lastName: lastName));
  }

  void setBuyerEmailAddress(String? emailAddress) {
    emit(state.copyWith(emailAddress: emailAddress));
  }

  void setBuyerPrimaryPhoneNumber(String? primaryPhoneNumber) {
    emit(state.copyWith(primaryPhoneNumber: primaryPhoneNumber));
  }

  void setBuyerOptionalPhoneNumber(String? optionalPhoneNumber) {
    emit(state.copyWith(optionalPhoneNumber: optionalPhoneNumber));
  }

  void addBuyerLocation(String location) {
    final locations = state.buyerLocationsOfInterest.toList();
    locations.add(location);
    emit(state.copyWith(buyerLocationsOfInterest: locations));
  }

  void syncBuyerLocationsOfInterests(List<String> buyerLocationsOfInterest) {
    emit(state
        .copyWith(buyerLocationsOfInterest: [...buyerLocationsOfInterest]));
  }

  void updateBuyerLocation(int index, String? location) {
    final locations = state.buyerLocationsOfInterest.toList();
    if (location != null) {
      locations[index] = location;
      emit(state.copyWith(buyerLocationsOfInterest: locations));
    }
  }

  void removeBuyerLocation(int index) {
    final locations = state.buyerLocationsOfInterest.toList();
    locations.removeAt(index);
    emit(state.copyWith(buyerLocationsOfInterest: locations));
  }

  void setBudget(String? budget) {
    emit(state.copyWith(budget: budget));
  }

  void setBedroomCount(double? bedroomCount) {
    emit(state.copyWith(bedroomCount: bedroomCount));
  }

  void setBathroomCount(double? bathroomCount) {
    emit(state.copyWith(bathroomCount: bathroomCount));
  }

  void setArea(double? area) {
    emit(state.copyWith(area: area));
  }

  void setTimeOffset(int? timeOffset) {
    emit(state.copyWith(timeOffset: timeOffset));
  }

  void addAdditionalDesire(String desire) {
    final desires = state.additionalRequests.toList();
    desires.add(desire);
    emit(state.copyWith(additionalRequests: [...desires]));
  }

  void syncAdditionalRequests(List<String> desires) {
    emit(state.copyWith(additionalRequests: [...desires]));
  }

  void updateAdditionalDesire(int index, String? desire) {
    final desires = state.additionalRequests.toList();
    if (desire != null) {
      desires[index] = desire;
      emit(state.copyWith(additionalRequests: [...desires]));
    }
  }

  void addAdditionalRequests(List<String> requests) {
    final desires = state.additionalRequests.toList();
    desires.addAll(requests);
    emit(state.copyWith(additionalRequests: [...desires]));
  }

  void addAdditionalRequestItem(String item) {
    final items = state.additionalRequests.toList();
    if (!items.contains(item)) {
      items.add(item);
      emit(state.copyWith(additionalRequests: [...items]));
    }
  }

  void removeAdditionalRequests(List<String> requests) {
    final desires = state.additionalRequests.toList();
    desires.removeWhere((element) => requests.contains(element));
    emit(state.copyWith(additionalRequests: [...desires]));
  }

  void removeAdditionalRequestItem(String item) {
    final items = state.additionalRequests.toList();
    items.remove(item);
    emit(state.copyWith(additionalRequests: [...items]));
  }

  void removeAdditionalDesire(int index) {
    final desires = state.additionalRequests.toList();
    desires.removeAt(index);
    emit(state.copyWith(additionalRequests: [...desires]));
  }

  void removeAdditionRequestItem(String item) {
    final items = state.additionalRequests.toList();
    items.remove(item);
    emit(state.copyWith(additionalRequests: [...items]));
  }

  void setAgreementExpirationDate(DateTime date) {
    print("DATE UPDATED: $date");
    emit(state.copyWith(buyerExpirationDate: date));
  }

  void resetBuyerInfo() => emit(BuyerInfo.initial());
}
