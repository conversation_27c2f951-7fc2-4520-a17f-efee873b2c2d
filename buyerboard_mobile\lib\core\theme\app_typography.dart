import 'package:buyer_board/core/theme/app_typography_extension.dart';
import 'package:flutter/material.dart';

abstract final class AppTypography {
  static _calculateHeight(double fontSize, double height) {
    return height / fontSize;
  }

  static AppTypographyExtension styles(Color textColor) {
    final coreTextStyle = TextStyle(
      fontFamily: 'Noto Sans',
      color: textColor,
    );
    final large5xReg = coreTextStyle.copyWith(
      fontSize: 61,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(61, 78),
    );

    final large4xReg = coreTextStyle.copyWith(
      fontSize: 49,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(49, 62),
    );

    final large3xReg = coreTextStyle.copyWith(
      fontSize: 39,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(39, 50),
    );

    final large2xReg = coreTextStyle.copyWith(
      fontSize: 31,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(31, 40),
    );

    final large1xReg = coreTextStyle.copyWith(
      fontSize: 25,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(25, 32),
    );

    final largeReg = coreTextStyle.copyWith(
      fontSize: 20,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(20, 25),
    );

    final mediumReg = coreTextStyle.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(16, 20),
    );

    final smallReg = coreTextStyle.copyWith(
      fontSize: 13,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(14, 16),
    );

    final small1xReg = coreTextStyle.copyWith(
      fontSize: 10,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(11, 12),
    );

    final small2xReg = coreTextStyle.copyWith(
      fontSize: 8,
      fontWeight: FontWeight.w400,
      height: _calculateHeight(8, 10),
    );

    return AppTypographyExtension(
      large5xReg: large5xReg,
      large5xSemi: large5xReg.copyWith(fontWeight: FontWeight.w600),
      large5xBlack: large5xReg.copyWith(fontWeight: FontWeight.w900),
      large4xReg: large4xReg,
      large4xSemi: large4xReg.copyWith(fontWeight: FontWeight.w600),
      large4xBlack: large4xReg.copyWith(fontWeight: FontWeight.w900),
      large3xReg: large3xReg,
      large3xSemi: large3xReg.copyWith(fontWeight: FontWeight.w600),
      large3xBlack: large3xReg.copyWith(fontWeight: FontWeight.w900),
      large2xReg: large2xReg,
      large2xSemi: large2xReg.copyWith(fontWeight: FontWeight.w600),
      large2xBlack: large2xReg.copyWith(fontWeight: FontWeight.w900),
      large1xReg: large1xReg,
      large1xSemi: large1xReg.copyWith(fontWeight: FontWeight.w600),
      large1xBlack: large1xReg.copyWith(fontWeight: FontWeight.w900),
      largeReg: largeReg,
      largeSemi: largeReg.copyWith(fontWeight: FontWeight.w600),
      largeBlack: largeReg.copyWith(fontWeight: FontWeight.w900),
      mediumReg: mediumReg,
      mediumSemi: mediumReg.copyWith(fontWeight: FontWeight.w600),
      mediumBlack: mediumReg.copyWith(fontWeight: FontWeight.w900),
      smallReg: smallReg,
      smallSemi: smallReg.copyWith(fontWeight: FontWeight.w600),
      smallBlack: smallReg.copyWith(fontWeight: FontWeight.w900),
      small1xReg: small1xReg,
      small1xSemi: small1xReg.copyWith(fontWeight: FontWeight.w600),
      small1xBlack: small1xReg.copyWith(fontWeight: FontWeight.w900),
      small2xReg: small2xReg,
      small2xSemi: small2xReg.copyWith(fontWeight: FontWeight.w600),
      small2xBlack: small2xReg.copyWith(fontWeight: FontWeight.w900),
    );
  }
}
