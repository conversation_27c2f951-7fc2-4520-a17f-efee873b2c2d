import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:developer';

import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/notification_manager/notification_service.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/add_buyer/presentation/screens/add_buyer_screen.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chat_states.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_notification_cubit.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_socket_connection_cubit.dart';
import 'package:buyer_board/features/chat/presentation/screens/chat_main_screen.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import 'package:buyer_board/features/favorites/presentation/screens/favorites_screen.dart';
import 'package:buyer_board/features/favorites/presentation/state/favourite_buyer_state.dart';
import 'package:buyer_board/features/home/<USER>/cubit/home_tab_cubit.dart';
import 'package:buyer_board/features/home/<USER>/screens/expanded_buyer_card.dart';
import 'package:buyer_board/features/home/<USER>/screens/home_screen.dart';
import 'package:buyer_board/features/home/<USER>/widgets/current_location_listeners.dart';
import 'package:buyer_board/features/intro/presentation/screens/app_onboarding_screen.dart';
import 'package:buyer_board/features/profile/presentation/screens/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

import '../../../../core/resources/resources.dart';
import '../../../add_buyer/data/models/buyer_model.dart';

// final ValueNotifier tabNotifier = ValueNotifier<int>(0);

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  void _handleNotificationClick(OSNotificationClickEvent event) {
    // developer.log(event.jsonRepresentation(), name: 'Notification');

    final additionalData = event.notification.additionalData;
    if (additionalData != null && additionalData.containsKey('click_action')) {
      // developer.log(additionalData.toString(), name: 'Data');
      String clickAction = additionalData['click_action'];
      switch (clickAction) {
        case 'buyercard-details':
          _navigateToCardDetailScreen(additionalData);
          break;
        case 'chat-details':
          _navigateToChatScreen(additionalData);
          break;
        default:
          break;
      }
    }
  }

  void _navigateToCardDetailScreen(Map<String, dynamic> data) {
    try {
      final jsonString = jsonEncode(data);
      Map<String, dynamic> jsonData = jsonDecode(jsonString);
      final buyerModel = BuyerModel.fromJson(jsonData['buyer_card']);
      Navigator.of(context).push(MaterialPageRoute(builder: (context) {
        return ExpandedBuyerCard(
          buyer: buyerModel,
          showChatWithMe: true,
        );
      }));
    } catch (e) {
      developer.log('$e', error: e, name: 'Error');
    }
  }

  void _navigateToChatScreen(Map<String, dynamic> data) {
    try {
      final jsonString = jsonEncode(data);
      Map<String, dynamic> jsonData = jsonDecode(jsonString);
      final chatGroupModel = ChatGroupModel.fromJson(jsonData['chat_data']);
      log(jsonData.toString());
      final extra = (buyer: chatGroupModel, agent: chatGroupModel.threads.last);
      // print(jsonData);
      context.pushNamed(
        PagePath.chatDetailsScreen,
        extra: extra,
      );
    } catch (e) {
      developer.log('$e', error: e, name: 'Error');
    }
  }

  @override
  void initState() {
    super.initState();
    final appPreferences = Injector.resolve<AppPreferences>();
    final userSession = context.read<UserSessionCubit>();
    final userId = appPreferences.getUser()?.id ?? userSession.state?.id;
    if (userId == null) {
      context.go(PagePath.authScreen);
      return;
    }
    NotificationService.login(userId);
    context.read<AllChatsBloc>().add(GetAllChats());

    context.read<FavouriteBuyersCubit>().getFavouriteBuyers();
    OneSignal.Notifications.addClickListener(_handleNotificationClick);
  }

  final _selectedItemColor = AppColors.primary;
  final _unselectedItemColor = AppColors.white;
  final _selectedBgColor = AppColors.primaryXLight;
  final _unselectedBgColor = Colors.transparent;
  bool isFavouriteBuyersAvailable = false;
  bool hasNewMessage = false;

  BottomNavigationBarItem navBarItem({required Widget icon}) =>
      BottomNavigationBarItem(
        icon: icon,
        label: '',
      );

  BottomNavigationBarItem navBarItemWithBadge(
          {required Widget icon, bool isBadgeTrue = false}) =>
      BottomNavigationBarItem(
          icon: Stack(children: <Widget>[
            icon,
            if (isBadgeTrue)
              const Positioned(
                // draw a red marble
                top: 3.0,
                right: 25.0,
                child: IgnorePointer(
                  child: Icon(Icons.brightness_1,
                      size: 14.0, color: Colors.redAccent),
                ),
              )
          ]),
          label: '');

  List<Widget> mainTabs = [
    const HomeScreen(),
    const FavoritesScreen(),
    const AddBuyerScreen(),
    const ChatMainScreen(),
    const ProfileScreen(),
  ];

  Color _getBgColor(int index) =>
      context.read<HomeBottomNarBarTabCubit>().state == index
          ? _selectedBgColor
          : _unselectedBgColor;

  Color _getItemColor(int index) =>
      context.read<HomeBottomNarBarTabCubit>().state == index
          ? _selectedItemColor
          : _unselectedItemColor;

  void _onItemTapped(int index) {
    setState(() {
      context.read<HomeBottomNarBarTabCubit>().changeTab(index);
    });
  }

  Widget _buildIcon(int index) {
    final isSelected = context.watch<HomeBottomNarBarTabCubit>().state == index;
    final itemData = [
      (
        child: SvgPicture.asset(
          Drawables.bbLogo,
          height: 24,
          width: 24,
          colorFilter: ColorFilter.mode(
            _getItemColor(index),
            BlendMode.srcIn,
          ),
        ),
        text: Strings.buyerBoard,
      ),
      if (isFavouriteBuyersAvailable)
        (
          child: SvgPicture.asset(
            isSelected || isFavouriteBuyersAvailable
                ? Drawables.favoriteSolid
                : Drawables.favoriteOutline,
            color: !isSelected ? AppColors.white : AppColors.primary,
          ),
          text: Strings.favorites,
        ),
      if (!isFavouriteBuyersAvailable)
        (
          child: SvgPicture.asset(
            isSelected || isFavouriteBuyersAvailable
                ? Drawables.favoriteSolid
                : Drawables.favoriteOutline,
          ),
          text: Strings.favorites,
        ),
      (
        child: SvgPicture.asset(
            isSelected ? Drawables.addBuyerSolid : Drawables.addBuyerOutline),
        text: Strings.addBuyer,
      ),
      (
        child: SvgPicture.asset(isSelected
            ? Drawables.chatBubbleSolid
            : Drawables.chatBubbleOutline),
        text: 'Chats',
      ),
      (
        child: SvgPicture.asset(isSelected
            ? Drawables.accountCircleSolid
            : Drawables.accountCircleOutline),
        text: Strings.profile,
      ),
    ];
    return SizedBox(
      width: double.infinity,
      height: kBottomNavigationBarHeight,
      child: Material(
        color: _getBgColor(index),
        child: GestureDetector(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              itemData[index].child,
              Text(itemData[index].text,
                  style: TextStyle(fontSize: 12, color: _getItemColor(index))),
            ],
          ),
          onTap: () => _onItemTapped(index),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _chatSocket.disconnect();
    OneSignal.Notifications.clearAll();
    OneSignal.Notifications.removeClickListener(_handleNotificationClick);
    super.dispose();
  }

  late ChatSocketConnectionCubit _chatSocket;

  @override
  Widget build(BuildContext context) {
    // BlocListener<AllChatsBloc, AllChatsState>(
    //   listener: (context, state) {
    //     if (state is AllChatsDataState) {
    //       final hasNewMessage = state.chats.any((chat) => chat.hasNewMessage);
    //       context.read<ChatTabBadgeCubit>().setBadge(hasNewMessage);
    //     }
    //   },
    // );
    _chatSocket = context.read<ChatSocketConnectionCubit>();
    return AppOnboardingScreen(
      child: MultiBlocListener(
        listeners: [
          BlocListener<ChatNotificationCubit, ChatNotificationState>(
            listener: (context, state) {
              if (state is ChatNotificationReceived) {
                final selectedTab =
                    context.read<HomeBottomNarBarTabCubit>().state;
                if (selectedTab == 3) return;
                CoreUtils.showCustomToast(context,
                    title: 'Unread Message',
                    subTitle: 'You have (1) unread message(s)');
              }
            },
          ),
          BlocListener<FavouriteBuyersCubit, FavouriteBuyerState>(
            listener: (context, state) {
              if (state is success) {
                List<BuyerModel> buyers = state.buyers;
                int favouriteCount =
                    buyers.where((buyer) => buyer.isFavourite).length;
                if (favouriteCount > 0) {
                  setState(() {
                    isFavouriteBuyersAvailable = true;
                  });
                } else {
                  setState(() {
                    isFavouriteBuyersAvailable = false;
                  });
                }
              }
            },
          ),
          BlocListener<AllChatsBloc, AllChatsState>(
            listener: (context, state) {
              if (state is AllChatsDataState) {
                Future.microtask(() {
                  setState(() {
                    hasNewMessage =
                        state.chats.any((chat) => chat.hasNewMessage);
                  });
                });
              }
            },
          ),
        ],
        child: CurrentLocationListeners(
          child: BlocBuilder<HomeBottomNarBarTabCubit, int>(
              // valueListenable: tabNotifier,
              builder: (context, tab) {
            return Scaffold(
              // backgroundColor: AppColors.primaryLight,
              bottomNavigationBar: Theme(
                data: Theme.of(context).copyWith(
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                ),
                child: BottomNavigationBar(
                  selectedFontSize: 0,
                  unselectedFontSize: 0,
                  // backgroundColor: AppColors.primary,
                  type: BottomNavigationBarType.fixed,
                  selectedItemColor: AppColors.white,
                  unselectedItemColor: AppColors.white,
                  onTap: (index) => tab = index,
                  currentIndex: tab,
                  items: List.generate(
                    5,
                    (index) => (index != 3 && index != 1)
                        ? navBarItem(icon: _buildIcon(index))
                        : navBarItemWithBadge(
                            icon: _buildIcon(index),
                            isBadgeTrue: index == 1
                                ? isFavouriteBuyersAvailable
                                : hasNewMessage,
                          ),
                  ),
                ),
              ),
              body: mainTabs[tab],
            );
          }),
        ),
      ),
    );
  }
}
