import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:buyer_board/features/settings/presentation/cubit/delete_account_cubit.dart';
import 'package:buyer_board/features/settings/presentation/widgets/setting_section_base_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class SettingsAccountSection extends StatefulWidget {
  const SettingsAccountSection({super.key});

  @override
  State<SettingsAccountSection> createState() => _SettingsAccountSectionState();
}

class _SettingsAccountSectionState extends State<SettingsAccountSection> {
  late final User? user;
  @override
  void initState() {
    super.initState();
    user = context.read<ProfileCubit>().getUserProfile();
  }

  Widget get loading => const Center(
        child: SizedBox(
          height: 120,
          child: Center(
            child: CupertinoActivityIndicator(),
          ),
        ),
      );

  @override
  Widget build(BuildContext context) {
    if (user == null) {
      return const Text('User not found');
    }
    final name =
        ('${user!.profile?.firstName} ${user!.profile?.lastName}').trim();
    final firstLetters =
        name.isEmpty ? '' : name.split(' ').map((e) => e[0]).join();
    final email = user!.profile?.agentEmail;
    final profile = user!.profile?.imageUrl;
    return SettingSectionBaseWidget(
      title: Strings.account,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            contentPadding: const EdgeInsets.symmetric(vertical: 4),
            leading: CircleAvatar(
              radius: 28,
              backgroundColor: context.appColors.pPXLight,
              backgroundImage: profile == null ? null : NetworkImage(profile),
              child: profile == null
                  ? firstLetters.isNotEmpty
                      ? Text(
                          firstLetters,
                          style: context.typography.largeBlack.copyWith(
                            color: context.appColors.whitePDark,
                          ),
                        )
                      : const Icon(
                          Icons.person,
                          size: 28,
                        )
                  : null,
            ),
            title: user?.profile?.firstName == null &&
                    user?.profile?.lastName == null
                ? null
                : Text(name, style: context.typography.largeSemi),
            titleTextStyle: context.typography.largeSemi,
            subtitle: email == null ? null : Text(email),
            subtitleTextStyle: context.typography.mediumSemi.copyWith(
              color: context.appColors.greyMediumGreyDefault,
            ),
          ),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                context.pushNamed(PagePath.updateLoginCredentialsScreen);
              },
              child: Text(
                Strings.updateLoginCredentials,
                style: context.typography.mediumSemi.copyWith(
                  color: context.appColors.pPXLight,
                ),
              ),
            ),
          ),
          spacerH8,
          BlocBuilder<DeleteAccountCubit, DeleteAccountState>(
              builder: (context, state) {
            return CommonButton(
              label: Strings.deleteMyAccount,
              action: () {
                context.read<DeleteAccountCubit>().delete(context);
              },
              backgroundColor: context.colorScheme.error,
              textColor: context.colorScheme.onError,
            );
          }),
          spacerH8,
          Text(
            '''This is legal disclaimer copy to inform the user about what will happen if they choose to delete their account. 
Habitant venenatis vestibulum phasellus id purus condimentum massa eget. Montes adipiscing nullam nunc id id non vel euismod interdum. In ac ridiculus mi a a euismod nulla integer.
''',
            style: AppStyles.medium.copyWith(
              color: context.appColors.greyMediumGreyDefault,
            ),
          )
        ],
      ),
    );
  }
}
