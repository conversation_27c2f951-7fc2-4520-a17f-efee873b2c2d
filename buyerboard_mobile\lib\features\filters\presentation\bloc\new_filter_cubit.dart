import 'package:buyer_board/common/models/wrapper.dart';
import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/filters/models/new_filter_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NewFilterCubit extends Cubit<NewFilterModel> {
  NewFilterCubit()
      : super(
          NewFilterModel(
            filters: FilterOption(),
            sortOptions: FilterSortOption(
              buyerLocationsOfInterest: FilterSortOrder.asc,
              insertedAt: FilterSortOrder.asc,
            ),
            page: 1,
            pageSize: AppConfig.environment == Environment.prod ? 100 : 5,
          ),
        );

  void setFiltersOption(FilterOption filters) {
    emit(state.copyWith(filters: Wrapped.value(filters)));
  }

  void setSortOption(FilterSortOption sortOptions) {
    emit(state.copyWith(sortOptions: Wrapped.value(sortOptions)));
  }

  void setPage(int? page) {
    emit(state.copyWith(page: Wrapped.value(page)));
  }

  void setPurchaseType(PurchaseType purchaseType) {
    emit(
      state.copyWith(
          filters: Wrapped.value(
        state.filters?.copyWith(
          purchaseType: Wrapped.value(purchaseType),
        ),
      )),
    );
  }

  void setPropertyType(List<PropertyType> propertyType) {
    // emit(state.copyWith(
    //     filters: state.filters.copyWith(
    //         propertyType: propertyType.isEmpty ? null : [...propertyType])));
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            propertyType: Wrapped.value(
              propertyType.isEmpty ? null : [...propertyType],
            ),
          ),
        ),
      ),
    );
  }

  void setFinancialStatus(List<FinancialStatus> financialStatus) {
    // emit(state.copyWith(
    //     filters: state.filters.copyWith(
    //         financialStatus:
    //             financialStatus.isEmpty ? null : [...financialStatus])));
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            financialStatus: Wrapped.value(
              financialStatus.isEmpty ? null : [...financialStatus],
            ),
          ),
        ),
      ),
    );
  }

  void setMinBedrooms(double minBedrooms) {
    // emit(state.copyWith(
    //     filters: state.filters.copyWith(minBedrooms: minBedrooms)));
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            minBedrooms: Wrapped.value(minBedrooms),
          ),
        ),
      ),
    );
  }

  void setMinBathrooms(double minBathrooms) {
    // emit(state.copyWith(
    //     filters: state.filters.copyWith(minBathrooms: minBathrooms)));
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            minBathrooms: Wrapped.value(minBathrooms),
          ),
        ),
      ),
    );
  }

  void setMinArea(double minArea) {
    // emit(state.copyWith(filters: state.filters.copyWith(minArea: minArea)));
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            minArea: Wrapped.value(minArea),
          ),
        ),
      ),
    );
  }

  void setSearchZipCode(String? searchZipCode) {
    // emit(state.copyWith(
    //     filters: state.filters.copyWith(searchZipCode: searchZipCode)));

    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            searchZipCode: Wrapped.value(searchZipCode),
          ),
        ),
      ),
    );
  }

  void setSortOptions(FilterSortOption sortOptions) {
    // emit(state.copyWith(sortOptions: sortOptions));
    emit(state.copyWith(sortOptions: Wrapped.value(sortOptions)));
  }

  void clearFilters() {
    final zipCode = state.filters?.searchZipCode;
    emit(
      NewFilterModel(
        filters: FilterOption(
          searchZipCode: zipCode,
        ),
        sortOptions: FilterSortOption(
          buyerLocationsOfInterest: FilterSortOrder.asc,
          insertedAt: FilterSortOrder.asc,
        ),
        page: 1,
        pageSize: AppConfig.environment == Environment.prod ? 100 : 5,
      ),
    );
  }

  void clearZipCode() {
    // emit(state.copyWith(filters: state.filters.copyWith(searchZipCode: null)));
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            searchZipCode: const Wrapped.value(null),
          ),
        ),
      ),
    );
  }

  void toggleHideMyBuyers() {
    emit(
      state.copyWith(
        filters: Wrapped.value(
          state.filters?.copyWith(
            hideMyBuyers: Wrapped.value(!state.filters!.hideMyBuyers),
          ),
        ),
      ),
    );
  }
}
