defmodule BuyerboardBackend do
  @moduledoc """
  BuyerboardBackend keeps the contexts that define your domain
  and business logic.

  Contexts are also responsible for managing your data, regardless
  if it comes from the database, an external API or others.
  """
  use Sentry.PlugCapture
  use Phoenix.Endpoint, otp_app: :buyerboard_backend

  plug Plug.Parsers,
    parsers: [:urlencoded, :multipart, :json],
    pass: ["*/*"],
    json_decoder: Phoenix.json_library()

  plug Sentry.PlugContext
end
