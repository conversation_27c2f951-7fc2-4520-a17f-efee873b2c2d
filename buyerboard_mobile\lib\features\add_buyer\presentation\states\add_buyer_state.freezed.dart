// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_buyer_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddBuyerState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(BuyerModel buyer, String message) success,
    required TResult Function(String? error) addBuyerError,
    required TResult Function(String imageUrl, String? message)
        uploadBuyerImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(BuyerModel buyer, String message)? success,
    TResult? Function(String? error)? addBuyerError,
    TResult? Function(String imageUrl, String? message)? uploadBuyerImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(BuyerModel buyer, String message)? success,
    TResult Function(String? error)? addBuyerError,
    TResult Function(String imageUrl, String? message)? uploadBuyerImage,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(addBuyerError value) addBuyerError,
    required TResult Function(uploadBuyerImage value) uploadBuyerImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(addBuyerError value)? addBuyerError,
    TResult? Function(uploadBuyerImage value)? uploadBuyerImage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(addBuyerError value)? addBuyerError,
    TResult Function(uploadBuyerImage value)? uploadBuyerImage,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddBuyerStateCopyWith<$Res> {
  factory $AddBuyerStateCopyWith(
          AddBuyerState value, $Res Function(AddBuyerState) then) =
      _$AddBuyerStateCopyWithImpl<$Res, AddBuyerState>;
}

/// @nodoc
class _$AddBuyerStateCopyWithImpl<$Res, $Val extends AddBuyerState>
    implements $AddBuyerStateCopyWith<$Res> {
  _$AddBuyerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$AddBuyerStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'AddBuyerState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(BuyerModel buyer, String message) success,
    required TResult Function(String? error) addBuyerError,
    required TResult Function(String imageUrl, String? message)
        uploadBuyerImage,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(BuyerModel buyer, String message)? success,
    TResult? Function(String? error)? addBuyerError,
    TResult? Function(String imageUrl, String? message)? uploadBuyerImage,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(BuyerModel buyer, String message)? success,
    TResult Function(String? error)? addBuyerError,
    TResult Function(String imageUrl, String? message)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(addBuyerError value) addBuyerError,
    required TResult Function(uploadBuyerImage value) uploadBuyerImage,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(addBuyerError value)? addBuyerError,
    TResult? Function(uploadBuyerImage value)? uploadBuyerImage,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(addBuyerError value)? addBuyerError,
    TResult Function(uploadBuyerImage value)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements AddBuyerState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$AddBuyerStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'AddBuyerState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(BuyerModel buyer, String message) success,
    required TResult Function(String? error) addBuyerError,
    required TResult Function(String imageUrl, String? message)
        uploadBuyerImage,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(BuyerModel buyer, String message)? success,
    TResult? Function(String? error)? addBuyerError,
    TResult? Function(String imageUrl, String? message)? uploadBuyerImage,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(BuyerModel buyer, String message)? success,
    TResult Function(String? error)? addBuyerError,
    TResult Function(String imageUrl, String? message)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(addBuyerError value) addBuyerError,
    required TResult Function(uploadBuyerImage value) uploadBuyerImage,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(addBuyerError value)? addBuyerError,
    TResult? Function(uploadBuyerImage value)? uploadBuyerImage,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(addBuyerError value)? addBuyerError,
    TResult Function(uploadBuyerImage value)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements AddBuyerState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$successImplCopyWith<$Res> {
  factory _$$successImplCopyWith(
          _$successImpl value, $Res Function(_$successImpl) then) =
      __$$successImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BuyerModel buyer, String message});
}

/// @nodoc
class __$$successImplCopyWithImpl<$Res>
    extends _$AddBuyerStateCopyWithImpl<$Res, _$successImpl>
    implements _$$successImplCopyWith<$Res> {
  __$$successImplCopyWithImpl(
      _$successImpl _value, $Res Function(_$successImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyer = null,
    Object? message = null,
  }) {
    return _then(_$successImpl(
      buyer: null == buyer
          ? _value.buyer
          : buyer // ignore: cast_nullable_to_non_nullable
              as BuyerModel,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$successImpl implements success {
  const _$successImpl({required this.buyer, required this.message});

  @override
  final BuyerModel buyer;
  @override
  final String message;

  @override
  String toString() {
    return 'AddBuyerState.success(buyer: $buyer, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$successImpl &&
            (identical(other.buyer, buyer) || other.buyer == buyer) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, buyer, message);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      __$$successImplCopyWithImpl<_$successImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(BuyerModel buyer, String message) success,
    required TResult Function(String? error) addBuyerError,
    required TResult Function(String imageUrl, String? message)
        uploadBuyerImage,
  }) {
    return success(buyer, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(BuyerModel buyer, String message)? success,
    TResult? Function(String? error)? addBuyerError,
    TResult? Function(String imageUrl, String? message)? uploadBuyerImage,
  }) {
    return success?.call(buyer, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(BuyerModel buyer, String message)? success,
    TResult Function(String? error)? addBuyerError,
    TResult Function(String imageUrl, String? message)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(buyer, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(addBuyerError value) addBuyerError,
    required TResult Function(uploadBuyerImage value) uploadBuyerImage,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(addBuyerError value)? addBuyerError,
    TResult? Function(uploadBuyerImage value)? uploadBuyerImage,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(addBuyerError value)? addBuyerError,
    TResult Function(uploadBuyerImage value)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class success implements AddBuyerState {
  const factory success(
      {required final BuyerModel buyer,
      required final String message}) = _$successImpl;

  BuyerModel get buyer;
  String get message;

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$addBuyerErrorImplCopyWith<$Res> {
  factory _$$addBuyerErrorImplCopyWith(
          _$addBuyerErrorImpl value, $Res Function(_$addBuyerErrorImpl) then) =
      __$$addBuyerErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$addBuyerErrorImplCopyWithImpl<$Res>
    extends _$AddBuyerStateCopyWithImpl<$Res, _$addBuyerErrorImpl>
    implements _$$addBuyerErrorImplCopyWith<$Res> {
  __$$addBuyerErrorImplCopyWithImpl(
      _$addBuyerErrorImpl _value, $Res Function(_$addBuyerErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$addBuyerErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$addBuyerErrorImpl implements addBuyerError {
  const _$addBuyerErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'AddBuyerState.addBuyerError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$addBuyerErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$addBuyerErrorImplCopyWith<_$addBuyerErrorImpl> get copyWith =>
      __$$addBuyerErrorImplCopyWithImpl<_$addBuyerErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(BuyerModel buyer, String message) success,
    required TResult Function(String? error) addBuyerError,
    required TResult Function(String imageUrl, String? message)
        uploadBuyerImage,
  }) {
    return addBuyerError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(BuyerModel buyer, String message)? success,
    TResult? Function(String? error)? addBuyerError,
    TResult? Function(String imageUrl, String? message)? uploadBuyerImage,
  }) {
    return addBuyerError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(BuyerModel buyer, String message)? success,
    TResult Function(String? error)? addBuyerError,
    TResult Function(String imageUrl, String? message)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (addBuyerError != null) {
      return addBuyerError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(addBuyerError value) addBuyerError,
    required TResult Function(uploadBuyerImage value) uploadBuyerImage,
  }) {
    return addBuyerError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(addBuyerError value)? addBuyerError,
    TResult? Function(uploadBuyerImage value)? uploadBuyerImage,
  }) {
    return addBuyerError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(addBuyerError value)? addBuyerError,
    TResult Function(uploadBuyerImage value)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (addBuyerError != null) {
      return addBuyerError(this);
    }
    return orElse();
  }
}

abstract class addBuyerError implements AddBuyerState {
  const factory addBuyerError(final String? error) = _$addBuyerErrorImpl;

  String? get error;

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$addBuyerErrorImplCopyWith<_$addBuyerErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$uploadBuyerImageImplCopyWith<$Res> {
  factory _$$uploadBuyerImageImplCopyWith(_$uploadBuyerImageImpl value,
          $Res Function(_$uploadBuyerImageImpl) then) =
      __$$uploadBuyerImageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String imageUrl, String? message});
}

/// @nodoc
class __$$uploadBuyerImageImplCopyWithImpl<$Res>
    extends _$AddBuyerStateCopyWithImpl<$Res, _$uploadBuyerImageImpl>
    implements _$$uploadBuyerImageImplCopyWith<$Res> {
  __$$uploadBuyerImageImplCopyWithImpl(_$uploadBuyerImageImpl _value,
      $Res Function(_$uploadBuyerImageImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageUrl = null,
    Object? message = freezed,
  }) {
    return _then(_$uploadBuyerImageImpl(
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$uploadBuyerImageImpl implements uploadBuyerImage {
  const _$uploadBuyerImageImpl({required this.imageUrl, this.message});

  @override
  final String imageUrl;
  @override
  final String? message;

  @override
  String toString() {
    return 'AddBuyerState.uploadBuyerImage(imageUrl: $imageUrl, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$uploadBuyerImageImpl &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, imageUrl, message);

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$uploadBuyerImageImplCopyWith<_$uploadBuyerImageImpl> get copyWith =>
      __$$uploadBuyerImageImplCopyWithImpl<_$uploadBuyerImageImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(BuyerModel buyer, String message) success,
    required TResult Function(String? error) addBuyerError,
    required TResult Function(String imageUrl, String? message)
        uploadBuyerImage,
  }) {
    return uploadBuyerImage(imageUrl, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(BuyerModel buyer, String message)? success,
    TResult? Function(String? error)? addBuyerError,
    TResult? Function(String imageUrl, String? message)? uploadBuyerImage,
  }) {
    return uploadBuyerImage?.call(imageUrl, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(BuyerModel buyer, String message)? success,
    TResult Function(String? error)? addBuyerError,
    TResult Function(String imageUrl, String? message)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (uploadBuyerImage != null) {
      return uploadBuyerImage(imageUrl, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(addBuyerError value) addBuyerError,
    required TResult Function(uploadBuyerImage value) uploadBuyerImage,
  }) {
    return uploadBuyerImage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(addBuyerError value)? addBuyerError,
    TResult? Function(uploadBuyerImage value)? uploadBuyerImage,
  }) {
    return uploadBuyerImage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(addBuyerError value)? addBuyerError,
    TResult Function(uploadBuyerImage value)? uploadBuyerImage,
    required TResult orElse(),
  }) {
    if (uploadBuyerImage != null) {
      return uploadBuyerImage(this);
    }
    return orElse();
  }
}

abstract class uploadBuyerImage implements AddBuyerState {
  const factory uploadBuyerImage(
      {required final String imageUrl,
      final String? message}) = _$uploadBuyerImageImpl;

  String get imageUrl;
  String? get message;

  /// Create a copy of AddBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$uploadBuyerImageImplCopyWith<_$uploadBuyerImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
