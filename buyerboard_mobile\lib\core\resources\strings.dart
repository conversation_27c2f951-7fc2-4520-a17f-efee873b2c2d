class Strings {
  Strings._();
  // Shared
  static const appName = 'BuyerBoard';
  static const String emptyString = '';
  static const String timeoutError = 'Connection timed out';
  static const reconnect = 'Reconnect';

  static const String commonError = 'Oops, something went wrong';
  static const String unauthorizedError = 'Unauthorized user';
  static const add = "Add";
  static const note = 'Note';
  static const delete = 'Delete';
  static const deleteMyAccount = 'DELETE MY ACCOUNT';

  static const search = 'Search';
  static const save = 'Save';

  static const splashHeadline = 'Bring Your Buyers Home';
  static const loginWithApple = "Login With Apple";
  static const loginWithGoogle = "Login With Google";
  static const loginWithEmail = "Login With Email/Password";
  static const termsOfService = "Terms of Service";
  static const privacyPolicy = "Privacy Policy";
  static const loginWithEmailPassword = "Login With Email / Password";
  static const enterANewPassword = "Enter A New Password";
  static const email = "Email";
  static const emailHint = "Enter your email";
  static const emailRequiredValidator = "Email is required";
  static const emailValidator = "Please enter a valid email address";
  static const password = "Password";
  static const passwordRequiredValidator = "Password is required";
  static const passwordLengthValidator = "Password is required";
  static const requiredFieldValidator = "Field Required";
  static const brokerageNameFieldValidator = "Brokerage name is required";

  static const passwordValidator =
      "Minimum 8 characters with special character";
  static const passwordHint = "Enter your password";
  static const reEnterPassword = "Re-Enter Password";
  static const reEnterPasswordHint = "Re-Enter your password";
  static const newPassword = "New password";
  static const newPasswordHint = "Set your new password";
  static const confirmNewPassword = "Confirm new Password";
  static const confirmNewPasswordHint = "Re-Enter your new password";
  static const passwordDontMatch = "Passwords don't match";
  static const rememberMe = "Remember Me";
  static const enableBiometric = "Enable biometric for login";
  static const forgetPassword = "Forget password?";
  static const forgetPasswordMessage =
      "Need help accessing your account? No problem. Enter an email address and we'll send you a one time code";
  static const enterCodeHere = "Enter Code Here";
  static const sendMeTheCode = "Send Me The Code";
  static const sendMeANewCode = "Send Me A New Code";
  static const codeNoteReceivedMessage =
      "This is copy text about not receiving the previous code. It will also direct users to use the button below to request a new code.";
  static const login = "Login";
  static const loginInstead = "Login Instead";
  static const signUpWithEmailPassword = "Sign Up With Email / Password";
  static const signUp = "Sign Up";
  static const signUpInstead = "Sign Up Instead";
  static const signMeUp = "Sign Me Up";
  static const updatePassword = "Update Password";
  static const resetPassword = "Reset Password";
  static const cancel = "Cancel";

  //Home Screen Tabs
  static const buyerBoard = "BuyerBoard";
  static const favorites = "Favorites";
  static const addBuyer = "Add Buyer";
  static const messages = "Messages";
  static const profile = "Profile";

  static const personalInformation = "Personal Information";
  static const brokerageInformation = "Brokerage Information";

  //Profile Screen
  static const edit = "Edit";
  static const firstName = "First Name";
  static const firstNameHint = "First Name";
  static const lastName = "Last Name";
  static const lastNamehint = "Last Name";
  static const licenseIdNo = 'License ID Number';
  static const emailAddress = "Email Address";
  static const emailAddressHint = "Email Address";
  static const primaryPhoneNumber = "Phone Number (Primary)";
  static const phoneNumberHint = "+1 (###) ###-####";
  static const alternatePhoneNumber = "Alternate Phone Number (Optional)";
  static const brokerageName = "Brokerage Name";
  static const brokerageNameHint = "Name";
  static const brokerageLicenseNumber = "Brokerage License Number";
  static const brokerageLicenseNumberHint = "License Number";
  static const brokerageStreetAddress = "Brokerage Street Address";
  static const brokerageStreetAddressHint = "Street Address";
  static const brokerageCity = "Brokerage City";
  static const brokerageCityHint = "City";
  static const state = "State";
  static const stateHint = "State";
  static const brokerageZipCode = "Brokerage Zip Code";
  static const brokerageZipCodeHint = "Zip Code";
  static const cropImage = "Crop Image";
  static const agentInformation = "Agent Information";

// Add buyer Screen
  static const buyerDetails = "Buyer Details";
  static const buyerDetailsDesc = "This information is confidential.";
  static const profilePicture = "Profile Picture";
  static const alias = "Alias";
  static const aliasHint = "Alias";
  // Buyer Location
  static const buyerLocation = "Buyer Locations of Interest";
  static const buyerLocationDesc =
      "This information will be visible to all. Tap \"+Add a Zip Code\" below to add more zip codes to the list.";
  static const zipCode = "Zip Code";
  static const zipCodeHint = "Zip Code";
  static const addAZipCode = "Add A Zip Code";
  // Buyer Needs
  static const buyerNeeds = "Buyer Needs";
  static const buyerNeedsDesc = "This information will be visible to all.";
  static const purchaseType = "Purchase Type";
  static const buy = "Buy";
  static const rent = "Rent";
  static const propertyType = "Property Type";
  static const singleFamilyHouse = "Single-Family House";
  static const townHouse = "Townhouse";
  static const condo = "Condo";
  static const apartment = "Apartment";
  static const multiFamilyHouse = "Multi-Family House";
  static const mobile = "Mobile";
  static const land = "Land";
  static const fixer = "Fixer";
  static const financialStatus = "Financial Status";
  static const preQualified = "Pre-Qualified";
  static const preApproved = "Pre-Approved";
  static const allCash = "All cash";
  static const undetermined = "Undetermined";
  static const budgetAmount = "Budget Amount (Up To)";
  static const value = "Value";
  static const bedroomsMin = "Bedrooms (Min)";
  static const bathroomsMin = "Bathrooms (Min)";
  static const sqFootageMin = "Sq. Ft. (Min)";
  static const bedrooms = "Bedrooms";
  static const bathrooms = "Bathrooms";
  static const sqFootage = "Square Footage";
  static const timeLine = "Timeline";
  static const asap = "ASAP";
  static const threeMonths = "3 MOs";
  static const sixMonths = "6 MOs";
  static const oneYear = "1 YR+";
  static const open = "Open";
  // Additional Desires
  // static const additionalDesires = "Additional Desires";
  static const additionalRequests = "Buyer Requests";
  static const additionalDeriresDesc =
      "The options below are buyer requested features.";
  static const desire = "Desire";
  static const desireHint = "Desire";
  static const addADesire = "Add A Desire";
  static const agreementExpirationDate = "Buyer Expiration Date";
  static const buyerExpirationDateDesc =
      "Default to 90 days from date BuyerCard is created.";
  static const expirationDate = "Expiration Date";
  static const expirationDateHint = "MM/DD/YYYY";

  //menu
  static const menu = "Menu";
  static const aboutBuyerBoard = "About BuyerBoard";
  static const help = "Help";
  static const faqs = "FAQs";
  static const settings = "Settings";

  //about BuyerBoard
  static const introToBuyerBoard = "Intro to BuyerBoard";

  //buyerboard privacy policy
  static const buyerBoardPrivacyPolicy = "BuyerBoard Privacy Policy";

  //help screen
  static const contactUs = "Contact Us...";
  static const stillNeedHelp = "Still need help? Send us a message.";
  static const contactUsDescription =
      "We are here to answer any of your questions. We will respond to you in 24-48 hours.";
  static const helpfulTopics = "Helpful Topics";
  static const helpfulTopicsDescription =
      "Below are some topics to help you navigate the BuyerBoard app.";
  static const wasThisHelpful = "Was this helpful?";
  static const createdOn = "Created On: ";
  static const buyerBrokerAgreement = "Buyer / Broker Agreement: ";
  static const topic = "Topic";
  static const needMoreHelp = "Need more help?";
  static const trySendingUsMessage =
      "Still need help? Try sending us a message.";
  static const relatedTopics = "Related Topics...";
  static const top10Questions = "Top 10 Questions";
  static const mostPopularQuestions =
      "Below are the most popular questions we get asked.";

  //button text
  static const done = "Done";
  static const next = "Next";
  static const previous = "Previous";
  static const sendUsAMessage = "Send us a message";
  static const expandAll = "Expand All";
  static const collapseAll = "Collapse All";
  static const addANote = "Add A Note";
  static const editNote = "Edit Note";
  static const update = "Update";
  static const enterYourNoteHere = "Enter your note here";

  // Settings
  static const updateLoginCredentials = "Update Login Credentials";
  static const menuSettings = "Menu > Setting";

  static const emailAndPassword = "Email / Password";
  static const emailPasswordSubtitle = "Update your email and password.";
  static const alternateLoginMethod = "Alternate Login Method";
  static const alternateLoginMethodSubtitle = "Update your login method.";
  static const appearance = "Appearance";
  static const appearanceSubtitle = "Choose your display preference";
  static const locationSharing = "Location Sharing";
  static const locationSharingSubtitle =
      "Discover buyers within your local area.";
  static const shareLocation = "Share Location";
  static const shareLocationSubtitle =
      "Your searches will now be based on your brokerage location.";
  static const notifications = "Notifications";
  static const notificationsSubtitle =
      "Choose how you would like to be notified.";
  static const incomingMessagesAgents = "Incoming Messages (Agents)";
  static const incomingMessagesBuyerBoardTeam =
      "Incoming Messages (BuyerBoard Team)";
  static const account = "Account";
  static const editChat = "edit";
  static const replyChat = "reply";
  static const copyChat = "copy";
  static const deleteChat = "delete";
  static const messageCopied = "message copied";
}
