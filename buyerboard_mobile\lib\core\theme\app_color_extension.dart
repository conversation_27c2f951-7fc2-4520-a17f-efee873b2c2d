import 'package:flutter/material.dart';

class AppColorsExtension extends ThemeExtension<AppColorsExtension> {
  AppColorsExtension({
    required this.pXLightPLight,
    required this.greyXLightPDark,
    required this.greyLightPDark,
    required this.whitePDark,
    required this.greyLightPDefault,
    required this.whitePXLight,
    required this.blackWhite,
    required this.pPXLight,
    required this.gBlackGLight,
    required this.whiteBlack,
    required this.whitePXDark,
    required this.greyM,
    required this.error,
    required this.black,
    required this.greyLightGreyDark,
    required this.pLightPMedium,
    required this.greyLightGreyDefault,
    required this.blackGreyMedium,
    required this.greyMediumGreyDefault,
    required this.greyDefaultGreyMedium,
    required this.greyLightPXDark,
    required this.greyXLightPXDark,
    required this.greyMP,
    required this.pLightGrey,
    required this.greyXLightGreyM,
    required this.white,
    required this.pBlack,
    required this.pLight,
    required this.greyMGreyL,
  });

  final Color pXLightPLight;
  final Color greyXLightPDark;
  final Color greyLightPDark;
  final Color whitePDark;
  final Color greyLightPDefault;
  final Color whitePXLight;
  final Color blackWhite;
  final Color whiteBlack;
  final Color whitePXDark;
  final Color greyM;
  final Color error;
  final Color black;
  final Color greyLightGreyDark;
  final Color pLightPMedium;
  final Color greyLightGreyDefault;
  final Color blackGreyMedium;
  final Color greyMediumGreyDefault;
  final Color greyDefaultGreyMedium;

  /// pPXLight color is used where light theme uses primary default
  /// color and dark theme uses primary x-light color
  final Color pPXLight;

  /// gBlackGLight color is used where light theme uses black color
  /// and dark theme uses grey light color
  final Color gBlackGLight;

  final Color greyLightPXDark;
  final Color greyXLightPXDark;
  final Color greyMP;
  final Color pLightGrey;
  final Color greyXLightGreyM;
  final Color white;
  final Color pBlack;
  final Color pLight;

  final Color greyMGreyL;

  @override
  ThemeExtension<AppColorsExtension> copyWith({
    Color? pXLightPLight,
    Color? greyXLightPDark,
    Color? greyLightPDark,
    Color? whitePDark,
    Color? greyLightPDefault,
    Color? whitePXLight,
    Color? blackWhite,
    Color? pPXLight,
    Color? gBlackGLight,
    Color? whiteBlack,
    Color? whitePXDark,
    Color? greyM,
    Color? error,
    Color? black,
    Color? greyLightGreyDark,
    Color? pLightPMedium,
    Color? greyLightGreyDefault,
    Color? blackGreyMedium,
    Color? greyMediumGreyDefault,
    Color? greyDefaultGreyMedium,
    Color? greyLightPXDark,
    Color? greyXLightPXDark,
    Color? greyMP,
    Color? pLightGrey,
    Color? greyXLightGreyM,
    Color? white,
    Color? pBlack,
    Color? pLight,
    Color? greyMGreyL,
  }) {
    return AppColorsExtension(
      pXLightPLight: pXLightPLight ?? this.pXLightPLight,
      greyXLightPDark: greyXLightPDark ?? this.greyXLightPDark,
      greyLightPDark: greyLightPDark ?? this.greyLightPDark,
      whitePXLight: whitePXLight ?? this.whitePXLight,
      whitePDark: whitePDark ?? this.whitePDark,
      greyLightPDefault: greyLightPDefault ?? this.greyLightPDefault,
      blackWhite: blackWhite ?? this.blackWhite,
      pPXLight: pPXLight ?? this.pPXLight,
      gBlackGLight: gBlackGLight ?? this.gBlackGLight,
      whiteBlack: whiteBlack ?? this.whiteBlack,
      whitePXDark: whitePXDark ?? this.whitePXDark,
      greyM: greyM ?? this.greyM,
      error: error ?? this.error,
      black: black ?? this.black,
      greyLightGreyDark: greyLightGreyDark ?? this.greyLightGreyDark,
      pLightPMedium: pLightPMedium ?? this.pLightPMedium,
      greyLightGreyDefault: greyLightGreyDefault ?? this.greyLightGreyDefault,
      blackGreyMedium: blackGreyMedium ?? this.blackGreyMedium,
      greyMediumGreyDefault:
          greyMediumGreyDefault ?? this.greyMediumGreyDefault,
      greyDefaultGreyMedium:
          greyDefaultGreyMedium ?? this.greyDefaultGreyMedium,
      greyLightPXDark: greyLightPXDark ?? this.greyLightPXDark,
      greyXLightPXDark: greyXLightPXDark ?? this.greyXLightPXDark,
      greyMP: greyMP ?? this.greyMP,
      pLightGrey: pLightGrey ?? this.pLightGrey,
      greyXLightGreyM: greyXLightGreyM ?? this.greyXLightGreyM,
      white: white ?? this.white,
      pBlack: pBlack ?? this.pBlack,
      pLight: pLight ?? this.pLight,
      greyMGreyL: greyMGreyL ?? this.greyMGreyL,
    );
  }

  @override
  ThemeExtension<AppColorsExtension> lerp(
    covariant ThemeExtension<AppColorsExtension>? other,
    double t,
  ) {
    if (other is! AppColorsExtension) {
      return this;
    }

    return AppColorsExtension(
      pXLightPLight: Color.lerp(pXLightPLight, other.pXLightPLight, t)!,
      greyXLightPDark: Color.lerp(greyXLightPDark, other.greyXLightPDark, t)!,
      greyLightPDark: Color.lerp(greyLightPDark, other.greyLightPDark, t)!,
      whitePDark: Color.lerp(whitePDark, other.whitePDark, t)!,
      greyLightPDefault:
          Color.lerp(greyLightPDefault, other.greyLightPDefault, t)!,
      whitePXLight: Color.lerp(whitePXLight, other.whitePXLight, t)!,
      blackWhite: Color.lerp(blackWhite, other.blackWhite, t)!,
      pPXLight: Color.lerp(pPXLight, other.pPXLight, t)!,
      gBlackGLight: Color.lerp(gBlackGLight, other.gBlackGLight, t)!,
      whiteBlack: Color.lerp(whiteBlack, other.whiteBlack, t)!,
      whitePXDark: Color.lerp(whitePXDark, other.whitePXDark, t)!,
      greyM: Color.lerp(greyM, other.greyM, t)!,
      error: Color.lerp(error, other.error, t)!,
      black: Color.lerp(black, other.black, t)!,
      greyLightGreyDark:
          Color.lerp(greyLightGreyDark, other.greyLightGreyDark, t)!,
      pLightPMedium: Color.lerp(pLightPMedium, other.pLightPMedium, t)!,
      greyLightGreyDefault:
          Color.lerp(greyLightGreyDefault, other.greyLightGreyDefault, t)!,
      blackGreyMedium: Color.lerp(blackGreyMedium, other.blackGreyMedium, t)!,
      greyMediumGreyDefault:
          Color.lerp(greyMediumGreyDefault, other.greyMediumGreyDefault, t)!,
      greyDefaultGreyMedium:
          Color.lerp(greyDefaultGreyMedium, other.greyDefaultGreyMedium, t)!,
      greyLightPXDark: Color.lerp(greyLightPXDark, other.greyLightPXDark, t)!,
      greyXLightPXDark:
          Color.lerp(greyXLightPXDark, other.greyXLightPXDark, t)!,
      greyMP: Color.lerp(greyMP, other.greyMP, t)!,
      pLightGrey: Color.lerp(pLightGrey, other.pLightGrey, t)!,
      greyXLightGreyM: Color.lerp(greyXLightGreyM, other.greyXLightGreyM, t)!,
      white: Color.lerp(white, other.white, t)!,
      pBlack: Color.lerp(pBlack, other.pBlack, t)!,
      pLight: Color.lerp(pLight, other.pLight, t)!,
      greyMGreyL: Color.lerp(greyMGreyL, other.greyMGreyL, t)!,
    );
  }
}
