import 'package:buyer_board/common/widgets/app_slide_transition.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'buyer_location_filter_cubit.dart';

class BuyerSearchSlideAnimationTypeCubit extends Cubit<AppSlideTransitionType> {
  BuyerSearchSlideAnimationTypeCubit()
      : super(AppSlideTransitionType.slideDown);

  void changeType({
    required BuildContext context,
    required LocationEntity newLocation,
    required LocationEntity oldLocation,
  }) {
    final values = context.read<BuyerFilterLocationsCubit>().state;
    if (values is BuyerLocationFilterLoaded) {
      final locations = values.locations;
      final index = locations.indexOf(newLocation);
      final oldLocationIndex = locations.indexOf(oldLocation);
      final type = index < oldLocationIndex
          ? AppSlideTransitionType.slideDown
          : AppSlideTransitionType.slideUp;
      emit(type);
    }
  }
}
