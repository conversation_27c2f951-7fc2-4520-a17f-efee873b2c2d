import 'package:buyer_board/common/widgets/profile_text_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/core/utils/text_field_formatters.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:flutter/material.dart';
import '../../../../core/resources/resources.dart';

class PersonalInformationSection extends StatelessWidget {
  const PersonalInformationSection({
    super.key,
    required this.firstNameController,
    required this.lastNameController,
    required this.emailAddressController,
    required this.primaryPhoneNumberController,
    required this.secondaryPhoneNumberController,
    required this.agentLicenseIdNoController,
    this.readOnly = true,
  });
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final TextEditingController emailAddressController;
  final TextEditingController primaryPhoneNumberController;
  final TextEditingController secondaryPhoneNumberController;
  final TextEditingController agentLicenseIdNoController;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.agentInformation,
          style: context.typography.large1xBlack.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        spacerH8,
        Row(
          children: [
            Flexible(
              child: ProfileTextField(
                controller: firstNameController,
                validator: Validators().requiredFieldValidator,
                label: Strings.firstName,
                hint: Strings.firstNameHint,
                textCapitalization: TextCapitalization.sentences,
                readOnly: readOnly,
              ),
            ),
            const SizedBox(width: 16),
            Flexible(
              child: ProfileTextField(
                controller: lastNameController,
                validator: Validators().requiredFieldValidator,
                label: Strings.lastName,
                hint: Strings.lastName,
                textCapitalization: TextCapitalization.sentences,
                readOnly: readOnly,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // License ID Number
        ProfileTextField(
          controller: agentLicenseIdNoController,
          keyboardType: TextInputType.text,
          // validator: Validators().requiredFieldValidator,
          validator: CoreUtils.agentLicenseNumber(),
          label: Strings.licenseIdNo,
          hint: Strings.licenseIdNo,
          readOnly: readOnly,
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: emailAddressController,
          keyboardType: TextInputType.emailAddress,
          // validator: Validators().requiredFieldValidator,
          validator: CoreUtils.emailValidator(),
          label: Strings.emailAddress,
          hint: Strings.emailAddressHint,
          readOnly: readOnly,
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: primaryPhoneNumberController,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            USPhoneNumberInputFormatter(),
          ],
          // validator: Validators().requiredFieldValidator,
          validator: CoreUtils.phoneNumberValidator(),
          label: Strings.primaryPhoneNumber,
          hint: Strings.phoneNumberHint,
          readOnly: readOnly,
        ),
        const SizedBox(height: 16),
        // ProfileTextField(
        //   controller: secondaryPhoneNumberController,
        //   keyboardType: TextInputType.phone,
        //   inputFormatters: [PhoneInputFormatter()],
        //   label: Strings.alternatePhoneNumber,
        //   hint: Strings.phoneNumberHint,
        //   readOnly: readOnly,
        // ),
        // const SizedBox(height: 16),
      ],
    );
  }
}
