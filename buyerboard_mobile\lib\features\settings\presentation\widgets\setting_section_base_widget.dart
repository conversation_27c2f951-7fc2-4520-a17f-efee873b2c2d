import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:flutter/material.dart';

class SettingSectionBaseWidget extends StatelessWidget {
  const SettingSectionBaseWidget({
    super.key,
    this.title,
    this.subtitle,
    this.child,
  });

  final String? title;
  final String? subtitle;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (title != null)
          Text(
            title!,
            style: context.typography.large1xBlack,
          ),
        if (subtitle != null)
          Text(
            subtitle!,
            style: context.typography.mediumReg,
          ),
        spacerH8,
        if (child != null) child!,
      ],
    );
  }
}
