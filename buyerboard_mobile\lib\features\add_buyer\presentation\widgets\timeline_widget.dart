// import 'package:buyer_board/core/extensions/build_context.dart';
// import 'package:flutter/material.dart';

// import '../../../../common/widgets/common_radio_tile.dart';
// import '../../../../core/resources/resources.dart';
// import '../cubit/buyer_info_cubit.dart';

// class TimeLineWidget extends StatefulWidget {
//   const TimeLineWidget({super.key});

//   @override
//   State<TimeLineWidget> createState() => _TimeLineWidgetState();
// }

// class _TimeLineWidgetState extends State<TimeLineWidget> {
//   void togleTimeline(TimeLine? val) {
//     TimeLine timeLine = timelineNotifier.value;
//     val != null && timeLine != val
//         ? timelineNotifier.value = val
//         : timelineNotifier.value = TimeLine.none;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return ValueListenableBuilder(
//         valueListenable: timelineNotifier,
//         builder: (context, timeLine, _) {
//           return Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 Strings.timeLine,
//                 style: context.typography.smallReg.copyWith(
//                   color: context.appColors.greyM,
//                 ),
//               ),
//               const SizedBox(height: 16),
//               CommonRadioTile<TimeLine>(
//                 label: Strings.asap,
//                 onChange: togleTimeline,
//                 value: TimeLine.asap,
//                 groupValue: timeLine,
//               ),
//               const SizedBox(height: 2),
//               CommonRadioTile<TimeLine>(
//                 label: Strings.threeMonths,
//                 onChange: togleTimeline,
//                 value: TimeLine.three_months,
//                 groupValue: timeLine,
//               ),
//               const SizedBox(height: 2),
//               CommonRadioTile<TimeLine>(
//                 label: Strings.sixMonths,
//                 onChange: togleTimeline,
//                 value: TimeLine.six_months,
//                 groupValue: timeLine,
//               ),
//               const SizedBox(height: 2),
//               CommonRadioTile<TimeLine>(
//                 label: Strings.open,
//                 onChange: togleTimeline,
//                 value: TimeLine.open,
//                 groupValue: timeLine,
//               ),
//             ],
//           );
//         });
//   }
// }
