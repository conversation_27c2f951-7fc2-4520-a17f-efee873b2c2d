import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:flutter/material.dart';

class LikeAndUnlikeButtonsWidget extends StatefulWidget {
  const LikeAndUnlikeButtonsWidget({super.key});

  @override
  State<LikeAndUnlikeButtonsWidget> createState() =>
      _LikeAndUnlikeButtonsWidgetState();
}

class _LikeAndUnlikeButtonsWidgetState
    extends State<LikeAndUnlikeButtonsWidget> {
  // bool? isUserLiked;
  bool? isHelpful;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _FeedbackButton(
          selected: isHelpful ?? false,
          icon: Icons.thumb_up_alt_outlined,
          label: 'Yes',
          onTap: () {
            setState(() {
              isHelpful = true;
            });
          },
        ),
        spacerW16,
        _FeedbackButton(
          selected: isHelpful != null && !isHelpful!,
          icon: Icons.thumb_down_alt_outlined,
          label: 'No',
          onTap: () {
            setState(() {
              isHelpful = false;
            });
          },
        ),
      ],
    );
    // final colors = context.colorScheme;
    // return Row(
    //   mainAxisSize: MainAxisSize.min,
    //   children: [
    //     Flexible(
    //       child: CommonButton(
    //         textColor: isUserLiked != null
    //             ? isUserLiked!
    //                 ? colors.onSurface
    //                 : colors.primary
    //             : colors.primary,
    //         backgroundColor: isUserLiked != null
    //             ? isUserLiked!
    //                 ? colors.primary
    //                 : Colors.transparent
    //             : Colors.transparent,
    //         borderColor: isUserLiked != null
    //             ? isUserLiked!
    //                 ? colors.white
    //                 : colors.primary
    //             : colors.primary,
    //         label: "Yes",
    //         action: () {
    //           isUserLiked = true;
    //           setState(() {});
    //         },
    //         buttonType: ButtonType.outline,
    //         leadingIcon: Icon(
    //           Icons.thumb_up_alt_outlined,
    //           color: isUserLiked != null
    //               ? isUserLiked!
    //                   ? colors.white
    //                   : colors.primary
    //               : colors.primary,
    //         ),
    //       ),
    //     ),
    //     const SizedBox(
    //       width: Dimensions.padding_8,
    //     ),
    //     Flexible(
    //         child: CommonButton(
    //       textColor: isUserLiked != null
    //           ? isUserLiked!
    //               ? colors.primary
    //               : AppColors.white
    //           : colors.primary,
    //       backgroundColor: isUserLiked != null
    //           ? isUserLiked!
    //               ? Colors.transparent
    //               : colors.primary
    //           : Colors.transparent,
    //       borderColor: isUserLiked != null
    //           ? isUserLiked!
    //               ? colors.primary
    //               : AppColors.white
    //           : colors.primary,
    //       label: "No",
    //       action: () {
    //         isUserLiked = false;
    //         setState(() {});
    //       },
    //       buttonType: ButtonType.outline,
    //       leadingIcon: Icon(
    //         Icons.thumb_down_alt_outlined,
    //         color: isUserLiked != null
    //             ? isUserLiked!
    //                 ? colors.primary
    //                 : AppColors.white
    //             : colors.primary,
    //       ),
    //     ))
    //   ],
    // );
  }
}

class _FeedbackButton extends StatelessWidget {
  const _FeedbackButton({
    required this.selected,
    required this.icon,
    required this.label,
    required this.onTap,
  });

  final bool selected;
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: OutlinedButton.icon(
        onPressed: onTap,
        label: Text(label),
        icon: Padding(
          padding: const EdgeInsetsDirectional.only(end: 18),
          child: Icon(icon, size: 18),
        ),
        style: FilledButton.styleFrom(
          foregroundColor: selected
              ? context.colorScheme.onPrimary
              : context.colorScheme.primary,
          backgroundColor:
              selected ? context.colorScheme.primary : Colors.transparent,
          minimumSize: const Size.fromHeight(34),
        ),
      ),
    );
  }
}
