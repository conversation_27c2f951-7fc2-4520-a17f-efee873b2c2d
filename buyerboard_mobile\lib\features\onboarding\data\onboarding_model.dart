import 'package:json_annotation/json_annotation.dart';
part 'onboarding_model.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class OnboardingModel {
  const OnboardingModel({
    required this.image,
    this.leading,
    this.heading,
    this.description,
    this.bullets,
    this.trailing,
  });
  final String image;
  final String? leading;
  final String? heading;
  final String? description;
  final List<String>? bullets;
  final String? trailing;
  factory OnboardingModel.fromJson(Map<String, dynamic> json) =>
      _$OnboardingModelFromJson(json);
}
