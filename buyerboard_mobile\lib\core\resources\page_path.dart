class PagePath {
  PagePath._();
  static const splash = '/';
  static const authScreen = '/authScreen';
  static const forgetPassword = '/forgetPassword';
  static const pinCodeScreen = '/forgetPassword/pinCodeScreen';
  static const updatePasswordScreen = '/updatePasswordScreen';
  static const onboardingScreen = '/onboarding';
  static const mainScreen = '/mainScreen';
  static const expandedBuyerCard = '/mainScreen/expandedBuyerCard';
  static const editBuyer = '/mainScreen/expandedBuyerCard/editBuyer';
  static const menu = "/menu";
  static const aboutBuyerBoard = "/aboutBuyerBoard";
  static const privacyPolicy = "/privacyPolicy";
  static const termsOfService = "/termsOfService";
  static const introToBuyerBoard = "/introToBuyerBoard";
  static const help = "/help";
  static const topic = "/topic";
  static const faq = "/faq";
  static const settingsScreen = "settings";
  static const updateLoginCredentialsScreen = "update-login-credentials";
  static const chatMainScreen = "/chat-main";
  static const chatDetailsScreen = 'chat-details';
  static const attachmentDetailsScreen = 'chat-details/attachments';
  static const attachmentDetails = 'chat-details/attachments/detail';
  static const filterScreen = "/mainScreen/filters";
  static const chatArchiveScreen = "/chat-archive";
}
