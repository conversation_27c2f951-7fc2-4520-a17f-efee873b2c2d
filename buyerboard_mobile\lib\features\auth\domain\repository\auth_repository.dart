import 'package:buyer_board/features/auth/data/models/requests/apple_login_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/auth_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/signup_auth_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/social_auth_request.dart';
import '../../../../core/network/base_response.dart';
import '../../data/models/response/auth_response.dart';

abstract class AuthRepository {
  Future<BaseResponse<User>> loginWithEmailPassword(
      {required AuthRequest loginWithEmailPasswordRequest});
  Future<BaseResponse<User>> signUpWithEmailPassword(
      {required SignUpAuthRequest signUpWithEmailPasswordRequest});
  Future<BaseResponse<User>> socialAuth(
      {required SocialAuthRequest socialAuthRequest});
  Future<dynamic> logOut();
  Future<BaseResponse<User>> loginWithApple({required AppleLoginRequest requestModel});
}
