import 'package:buyer_board/common/widgets/app_switch_tile.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/features/settings/presentation/cubit/notifications_preferences_cubit.dart';
import 'package:buyer_board/features/settings/presentation/widgets/setting_section_base_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SettingsNotificationSection extends StatelessWidget {
  const SettingsNotificationSection({super.key});

  @override
  Widget build(BuildContext context) {
    return SettingSectionBaseWidget(
      title: Strings.notifications,
      subtitle: Strings.notificationsSubtitle,
      child: BlocBuilder<
          NotificationsPreferencesCubit,
          ({
            bool incomingMessagesAgents,
            bool incomingMessagesBuyerBoard,
          })>(builder: (context, state) {
        return Column(
          children: [
            AppSwitch(
              title: Strings.incomingMessagesAgents,
              isActive: state.incomingMessagesAgents,
              onChanged: (value) {
                context
                    .read<NotificationsPreferencesCubit>()
                    .updateIncomingMessagesAgents(value);
              },
            ),
            AppSwitch(
              title: Strings.incomingMessagesBuyerBoardTeam,
              isActive: state.incomingMessagesBuyerBoard,
              onChanged: (value) {
                context
                    .read<NotificationsPreferencesCubit>()
                    .updateIncomingMessagesBuyerBoard(value);
              },
            ),
          ],
        );
      }),
    );
  }
}
