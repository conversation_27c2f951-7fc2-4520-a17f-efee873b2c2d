// import 'package:flutter/material.dart';

// import '../../../../core/resources/resources.dart';
// import '../../../../core/resources/text_styles.dart';

// class MessagesScreen extends StatefulWidget {
//   const MessagesScreen({super.key});

//   @override
//   State<MessagesScreen> createState() => _MessagesScreenState();
// }

// class _MessagesScreenState extends State<MessagesScreen> {
//   BottomNavigationBarItem navBarItem(
//           {required String label, required Widget icon}) =>
//       BottomNavigationBarItem(icon: icon, label: label);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       // backgroundColor: AppColors.primaryLight,
//       appBar: AppBar(
//         title: Text(
//           Strings.messages,
//           style: AppStyles.medium.copyWith(
//             fontSize: 20,
//             color: AppColors.white,
//           ),
//         ),
//         // backgroundColor: AppColors.primary,
//         centerTitle: true,
//       ),
//     );
//   }
// }
