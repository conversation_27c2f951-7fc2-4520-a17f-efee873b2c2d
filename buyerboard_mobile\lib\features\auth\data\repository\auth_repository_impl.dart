import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/features/auth/data/models/requests/apple_login_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/signup_auth_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/social_auth_request.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/auth/domain/repository/auth_repository.dart';
import '../../../../core/network/base_response.dart';
import '../models/requests/auth_request.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl({required this.restAPIClient});
  final RestAPIClient restAPIClient;
  @override
  Future<BaseResponse<User>> loginWithEmailPassword(
      {required AuthRequest loginWithEmailPasswordRequest}) async {
    return await restAPIClient.loginWithEmailPassword(
        loginRequest: loginWithEmailPasswordRequest);
  }

  @override
  Future<BaseResponse<User>> signUpWithEmailPassword(
      {required SignUpAuthRequest signUpWithEmailPasswordRequest}) async {
    return await restAPIClient.signUpWithEmailPassword(
        signupRequest: signUpWithEmailPasswordRequest);
  }

  @override
  Future<BaseResponse<User>> socialAuth(
      {required SocialAuthRequest socialAuthRequest}) async {
    return await restAPIClient.socialAuth(socialAuthRequest: socialAuthRequest);
  }

  @override
  Future<dynamic> logOut() async => await restAPIClient.logOut();

  @override
  Future<BaseResponse<User>> loginWithApple(
      {required AppleLoginRequest requestModel}) async {
    return await restAPIClient.loginWithApple(appleLoginRequest: requestModel);
  }
}
