import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/auth/presentation/cubit/logout_cubit.dart';
import 'package:buyer_board/features/menu/presentation/widgets/menu_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';

class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    return Scaffold(
      appBar: ApplicationAppBar.buildAppBar(
        context,
        title: Strings.menu,
        leadingWidget: IconButton(
            icon: const Icon(
              Icons.close,
              size: Dimensions.padding_28,
            ),
            color: AppColors.white,
            onPressed: () {
              context.shouldPop();
            }),
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          MenuItem(
            title: Strings.aboutBuyerBoard,
            leadingIcon: SvgPicture.asset(
              Drawables.bbLogo,
              height: Dimensions.padding_24,
              width: Dimensions.padding_24,
              color: colorScheme.primary,
            ),
            trailingIcon: Icon(
              Icons.arrow_forward_ios,
              size: Dimensions.materialPadding,
              color: colorScheme.onSurfaceVariant,
            ),
            onTapMenuItem: () => context.push(PagePath.aboutBuyerBoard),
          ),
          MenuItem(
            title: Strings.help,
            leadingIcon: Icon(
              Icons.help,
              color: context.colorScheme.primary,
              size: Dimensions.padding_24,
            ),
            onTapMenuItem: () => context.push(PagePath.help),
            trailingIcon: Icon(
              Icons.arrow_forward_ios,
              size: Dimensions.materialPadding,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          // MenuItem(
          //   title: Strings.faqs,
          //   leadingIcon: SvgPicture.asset(
          //     Drawables.icFaq,
          //     height: Dimensions.padding_24,
          //     width: Dimensions.padding_24,
          //     color: colorScheme.primary,
          //   ),
          //   onTapMenuItem: () => context.push(PagePath.faq),
          //   trailingIcon: const Icon(
          //     Icons.arrow_forward_ios,
          //     size: Dimensions.materialPadding,
          //     color: AppColors.greyMedium,
          //   ),
          // ),
          MenuItem(
            title: Strings.settings,
            leadingIcon: Icon(
              Icons.settings,
              color: context.colorScheme.primary,
              size: Dimensions.padding_24,
            ),
            onTapMenuItem: () {
              context.pushNamed(PagePath.settingsScreen);
            },
            trailingIcon: Icon(
              Icons.arrow_forward_ios,
              size: Dimensions.materialPadding,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          Container(
            decoration: BoxDecoration(
                border: Border.symmetric(
              horizontal: BorderSide(
                color: context.colorScheme.outlineVariant,
                width: 1,
              ),
            )),
            child: MenuItem(
              leadingTitle: Image.asset(
                Drawables.copyRightIcon,
                width: 16,
              ),
              title: "${Strings.buyerBoard} ${DateTime.now().year}",
              subtitle: const _VersionNumber(),
              titleTextStyle: context.typography.largeSemi.copyWith(
                color: context.appColors.greyM,
              ),
            ),
          ),
          spacerH16,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: CommonButton(
              backgroundColor: context.colorScheme.inverseSurface,
              textColor: context.colorScheme.onInverseSurface,
              label: 'Logout',
              action: () {
                context.read<LogoutCubit>().logout(context);
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _VersionNumber extends StatelessWidget {
  const _VersionNumber();

  @override
  Widget build(BuildContext context) {
    final versionSuffix = switch (AppConfig.environment) {
      Environment.prod => '',
      Environment.staging => ' (STG)',
      Environment.dev => ' (DEV)',
    };
    return FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        return switch (snapshot.connectionState) {
          ConnectionState.done => Text(
              'Version ${snapshot.data!.version} $versionSuffix'.trim(),
              style: context.typography.mediumSemi.copyWith(
                color: context.appColors.greyLightGreyDefault,
              ),
            ),
          _ => const SizedBox(),
        };
      },
    );
  }
}
