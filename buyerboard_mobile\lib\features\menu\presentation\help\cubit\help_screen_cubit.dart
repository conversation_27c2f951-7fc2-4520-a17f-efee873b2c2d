import 'dart:convert';

import 'package:buyer_board/features/menu/presentation/help/state/help_screen_state.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/help_response.dart';

class HelpScreenCubit extends Cubit<HelpScreenState> {
  HelpScreenCubit() : super(const HelpScreenState.initial());

  Future<void> getHelpTopics() async {
    emit(const HelpScreenState.loading());
    try {
      final source = await rootBundle.loadString("assets/json/help_description.json");
      final json = jsonDecode(source) as List<dynamic>;
      final response = json.map((e) => HelpResponse.fromJson(e)).toList() ;
      emit(HelpScreenState.success(response));
    } catch (e) {
      emit(HelpScreenState.helpScreenError(e.toString()));
    }
  }
}
