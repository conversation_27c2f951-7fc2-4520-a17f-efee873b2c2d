import 'dart:convert';

import 'package:buyer_board/features/onboarding/data/onboarding_model.dart';
import 'package:buyer_board/features/onboarding/presentation/states/onboarding_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

final onBoardingScreenIndexNotifier = ValueNotifier<int>(0);

class OnBoardingCubit extends Cubit<OnBoardingState> {
  OnBoardingCubit() : super(const OnBoardingState.initial());

  Future<void> initOnboarding() async {
    final source = await rootBundle.loadString("assets/json/onboarding.json");
    final json = jsonDecode(source) as List<dynamic>;
    final onBoardingData =
        json.map((e) => OnboardingModel.fromJson(e)).toList();
    emit(OnBoardingState.success(onBoardingData));
  }
}
