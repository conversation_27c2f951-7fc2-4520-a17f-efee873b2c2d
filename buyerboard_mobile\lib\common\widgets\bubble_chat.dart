import 'package:flutter/material.dart';

class ChatBubble extends StatelessWidget {
  final Widget child;
  final Color color;
  final bool hasTail;

  const ChatBubble({
    super.key,
    required this.child,
    required this.color,
    this.hasTail = true,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: CustomPaint(
        painter: _Cha<PERSON>BubbleClipper(
          color: color,
          hasTail: hasTail,
        ),
        child: Container(
          margin: const EdgeInsets.fromLTRB(12, 8, 16, 8),
          child: Padding(
            padding: const EdgeInsets.only(left: 4, right: 4),
            child: child,
          ),
        ),
      ),
    );
  }
}

class _ChatBubbleClipper extends CustomPainter {
  final Color color;
  final bool hasTail;

  _ChatBubbleClipper({
    required this.color,
    this.hasTail = true,
  });

  final double _radius = 8;

  @override
  void paint(Canvas canvas, Size size) {
    var h = size.height;
    var w = size.width;
    if (hasTail) {
      var path = Path();

      /// starting point
      path.moveTo(_radius * 3, 0);

      /// top-left corner
      path.quadraticBezierTo(_radius, 0, _radius, _radius * 1.5);

      /// left line
      path.lineTo(_radius, h - _radius * 1.5);
      // bottom-right tail curve 1
      path.quadraticBezierTo(_radius * .8, h, 0, h);

      /// bottom-right tail curve 2
      path.quadraticBezierTo(_radius * 1, h, _radius * 1.5, h - _radius * 0.6);

      /// bottom-left bubble curve
      path.quadraticBezierTo(_radius * 1.5, h, _radius * 3, h);

      /// bottom line
      path.lineTo(w - _radius * 2, h);

      /// bottom-right curve
      path.quadraticBezierTo(w, h, w, h - _radius * 1.5);

      /// right line
      path.lineTo(w, _radius * 1.5);

      /// top-right curve
      path.quadraticBezierTo(w, 0, w - _radius * 2, 0);
      canvas.clipPath(path);
      canvas.drawRRect(
          RRect.fromLTRBR(0, 0, w, h, Radius.zero),
          Paint()
            ..color = color
            ..style = PaintingStyle.fill);
    } else {
      var path = Path();

      /// starting point
      path.moveTo(_radius * 3, 0);

      /// top-left corner
      path.quadraticBezierTo(_radius, 0, _radius, _radius * 1.5);

      /// left line
      path.lineTo(_radius, h - _radius * 1.5);

      /// bottom-left curve
      path.quadraticBezierTo(_radius, h, _radius * 3, h);

      /// bottom line
      path.lineTo(w - _radius * 2, h);

      /// bottom-right curve
      path.quadraticBezierTo(w, h, w, h - _radius * 1.5);

      /// right line
      path.lineTo(w, _radius * 1.5);

      /// top-right curve
      path.quadraticBezierTo(w, 0, w - _radius * 2, 0);
      canvas.clipPath(path);
      canvas.drawRRect(
          RRect.fromLTRBR(0, 0, w, h, Radius.zero),
          Paint()
            ..color = color
            ..style = PaintingStyle.fill);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
