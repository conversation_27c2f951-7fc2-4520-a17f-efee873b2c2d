import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/utils/screen_utils.dart';
import 'package:buyer_board/features/settings/presentation/widgets/settings_account_section.dart';
import 'package:buyer_board/features/settings/presentation/widgets/settings_location_sharing_section.dart';
import 'package:buyer_board/features/settings/presentation/widgets/settings_notification_section.dart';
import 'package:buyer_board/features/settings/presentation/widgets/settings_theme_section.dart';
import 'package:flutter/material.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(Strings.settings),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SettingsThemeSection(),
            spacerH20,
            const SettingsLocationSharingSection(),
            spacerH24,
            const SettingsNotificationSection(),
            spacerH24,
            const SettingsAccountSection(),
            SizedBox(height: ScreenUtils.bottomPadding(context) + 12)
          ],
        ),
      ),
    );
  }
}
