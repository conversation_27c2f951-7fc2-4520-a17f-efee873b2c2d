defmodule BuyerboardBackend.Organization do
  use Ecto.Schema
  import Ecto.Changeset

  schema "organizations" do
    field :company_name, :string
    field :real_estate_lisence_no, :string
    field :broker_lisence_no, :string
    field :state, :string
    field :zip_codes, {:array, :integer}
    field :search_range, :string

    timestamps(type: :utc_datetime)
  end

  def changeset(organization, attrs) do
    organization
    |> cast(attrs, [
      :company_name,
      :real_estate_lisence_no,
      :broker_lisence_no,
      :state,
      :zip_codes,
      :search_range
    ])
  end
end
