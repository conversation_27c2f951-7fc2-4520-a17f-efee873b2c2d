import 'package:dio/dio.dart';

import '../../resources/resources.dart';

class ErrorHandlerInterceptor extends Interceptor {
  final int notAuthenticatedCode = 401;
  final int serverErrorCode = 500;
  final errorTag = 'Error Intercepted: ';
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        throw _TimeoutException(requestOptions: err.requestOptions);
      case DioExceptionType.sendTimeout:
        throw _TimeoutException(requestOptions: err.requestOptions);
      case DioExceptionType.receiveTimeout:
        throw _TimeoutException(requestOptions: err.requestOptions);
      case DioExceptionType.badResponse:
        {
          if (err.response != null &&
              err.response!.statusCode! >= serverErrorCode) {
            throw _CommonException(requestOptions: err.requestOptions);
          } else {
            throw _BackendException(
                requestOptions: err.requestOptions, response: err.response);
          }
        }
      case DioExceptionType.unknown:
        throw _TimeoutException(requestOptions: err.requestOptions);

      case DioExceptionType.badCertificate:
      case DioExceptionType.cancel:
      case DioExceptionType.connectionError:
    }
    return handler.next(err);
  }
}

class _TimeoutException extends DioException {
  _TimeoutException({required super.requestOptions});

  @override
  String toString() {
    return Strings.timeoutError;
  }
}

class _CommonException extends DioException {
  _CommonException({required super.requestOptions});

  @override
  String toString() {
    return Strings.commonError;
  }
}

class NoInternetConnectionException extends DioException {
  NoInternetConnectionException({required super.requestOptions}) {
    _showNoInternetScreen();
  }

  void _showNoInternetScreen() {
    // YallaGrubApp.appContext?.push(PagePath.noInternetConnection);
  }

  @override
  String toString() {
    return Strings.emptyString;
  }
}

class ErrorObjectException extends DioException {
  String _message = '';
  Map<String, dynamic>? errorData;
  ErrorObjectException({
    required super.requestOptions,
    required Response? response,
  }) {
    final data = response?.data as Map<String, dynamic>;
    errorData = data['error'];
    _message = errorData.toString();
  }

  @override
  String get message => _message;
  Map<String, dynamic>? get errorObject => errorData;

  @override
  String toString() => message;
}

class _BackendException extends DioException {
  String _message = '';
  Map<String, dynamic>? errorData;
  _BackendException({
    required super.requestOptions,
    required Response? response,
  }) {
    final data = response?.data as Map<String, dynamic>;
    errorData = data['error'];
    if (errorData?.containsKey('message') ?? false) {
      final messageData = errorData!['message'].toString();
      _message = messageData.isNotEmpty ? messageData : Strings.commonError;
    } else {
      errorData = data['errors'];
      if (errorData?.containsKey('detail') ?? false) {
        final messageData = errorData!['detail'].toString();
        _message = messageData.isNotEmpty ? messageData : Strings.commonError;
      } else {
        throw ErrorObjectException(
          requestOptions: super.requestOptions,
          response: response,
        );
      }

      // _message = errorData.toString();
      // Strings.commonError;
    }
  }

  @override
  String get message => _message;
  Map<String, dynamic>? get errorObject => errorData;

  @override
  String toString() => message;
}
