import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/domain/repositories/archived_chat_repository.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chat_states.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chats_events.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AllArchiveChatsBloc
    extends Bloc<AllArchiveChatsEvent, AllArchiveChatsState> {
  final ArchivedChatRepository chatRepository;
  final List<ChatGroupModel> _archiveMessages = [];

  AllArchiveChatsBloc({required this.chatRepository})
      : super(AllArchiveChatsInitialState()) {
    on<GetAllArchiveChats>(onLoadAllArchiveChat);
    on<UnArchiveChat>(_onUnarchiveChat);
    on<CloseArchiveChatConnection>((event, emit) {
      chatRepository.closeConnection();
    });
  }

  void onLoadAllArchiveChat(
      GetAllArchiveChats event, Emitter<AllArchiveChatsState> emit) async {
    _archiveMessages.clear();
    chatRepository.initializeConnection();
    emit(AllArchiveChatsLoadingState());
    try {
      final response = chatRepository.getArchivedChats();
      await for (final record in response) {
        _archiveMessages
          ..clear()
          ..addAll(record.data);
        emit(AllArchiveChatsDataState(
            List<ChatGroupModel>.from(_archiveMessages)));
      }
    } catch (e) {
      emit(AllArchiveChatsErrorState(e.toString()));
    }
  }

  void _onUnarchiveChat(
      UnArchiveChat event, Emitter<AllArchiveChatsState> emit) async {
    chatRepository.unarchiveChat(event.payload);
  }
}
