import 'dart:developer';

import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chat_states.dart';
import 'package:buyer_board/features/chat/presentation/widgets/chat_expandable_thread.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/all_archive_chats_bloc.dart';

class ChatArchiveScreen extends StatefulWidget {
  const ChatArchiveScreen({super.key});

  @override
  State<ChatArchiveScreen> createState() => _ChatArchiveScreenState();
}

class _ChatArchiveScreenState extends State<ChatArchiveScreen> {

  @override
  Widget build(BuildContext context) {
    final colors = context.theme.appColors;
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Archived'),
          centerTitle: true,
          bottom: PreferredSize(
            preferredSize: Size.zero,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                "Chats",
                style: context.typography.small1xReg
                    .copyWith(color: colors.pLightPMedium),
              ),
            ),
          ),
        ),
        body: const Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [_ChatMainListing()],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ChatMainListing extends StatelessWidget {
  const _ChatMainListing({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AllArchiveChatsBloc, AllArchiveChatsState>(
        builder: (context, state) {
          log("build data ");
      return switch (state) {
        AllArchiveChatsInitialState() ||
        AllArchiveChatsLoadingState() =>
          SizedBox(
            height: MediaQuery.sizeOf(context).height * .3,
            child: Center(
              child: CupertinoActivityIndicator(
                color: context.appColors.pPXLight,
              ),
            ),
          ),
        AllArchiveChatsDataState(chats: List<ChatGroupModel> chats) =>
          chats.isEmpty
              ? SizedBox(
                  height: 120,
                  child: Center(
                    child: Text(
                      'No Archived Chat',
                      style: context.typography.largeBlack.copyWith(
                        color: context.appColors.greyM,
                      ),
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(8),
                  itemBuilder: (_, index) => ChatExpandableThread(
                    isArchiveMode: false,
                    chatBuyerModel: chats[index],
                    key: ValueKey(chats[index].sku),
                  ),
                  itemCount: chats.length,
                ),
        _ => const Text('Error'),
      };
    });
  }
}
