import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';

sealed class BuyersState {}

final class BuyersLoading extends BuyersState {
  BuyersLoading();
}

final class BuyersLoaded extends BuyersState {
  final List<BuyerModel> buyers;
  final bool hasReachedMax;

  BuyersLoaded(this.buyers, {this.hasReachedMax = false});
}

// final class SearchResultsLoaded extends BuyersState {
//   final List<BuyerModel> searchResults;

//   SearchResultsLoaded(this.searchResults);
// }

final class BuyersError extends BuyersState {
  final String error;

  BuyersError(this.error);
}
