version: '3.9'

services:
  phoenix:
    build:
      context: .
    environment:
      PGUSER: postgres
      PGPASSWORD: postgres
      PGPORT: 5432
      PGHOST: postgres
    ports:
      - '4000:4000'
    depends_on:
      postgres:
        condition: service_healthy  # Wait for PostgreSQL to be healthy

  postgres:
    image: postgis/postgis
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: buyerboard_board_dev
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - '5432:5432'
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./buyer_board_dev.sql:/docker-entrypoint-initdb.d/buyer_board_dev.sql:ro

volumes:
  pgdata:
