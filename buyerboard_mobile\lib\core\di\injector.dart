import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/di/modules/app_module.dart';
import 'package:buyer_board/core/di/modules/cubit_module.dart';
import 'package:buyer_board/core/di/modules/network_module.dart';
import 'package:buyer_board/core/di/modules/repository_module.dart';
import 'package:buyer_board/core/di/modules/usecases_module.dart';
import 'package:kiwi/kiwi.dart';

abstract class Injector {
  static final KiwiContainer _container = KiwiContainer();
  static final resolve = _container.resolve;

  static Future<void> setup({required AppConfig appConfig}) async {
    _container.registerSingleton((_) => appConfig);
    await AppModule.setup(container: _container, appConfig: appConfig);
    NetworkModule.setup(container: _container, appConfig: appConfig);
    RepositoryModule.setup(_container);
    UseCasesInjection.setup(_container);
    CubitAndBlocModule.setup(_container);
  }

  static void clear() {
    _container.clear();
  }
}
