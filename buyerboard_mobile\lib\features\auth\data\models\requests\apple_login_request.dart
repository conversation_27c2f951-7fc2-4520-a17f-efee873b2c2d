import 'package:json_annotation/json_annotation.dart';

part 'apple_login_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, createFactory: false)
class AppleLoginRequest {
  final String? email;
  final String appleIdentifier;
  final String provider;
  final String? firstName;
  final String? lastName;

  const AppleLoginRequest({
    required this.email,
    required this.appleIdentifier,
    required this.provider,
    required this.firstName,
    required this.lastName,
  });

  Map<String, dynamic> toJson() => _$AppleLoginRequestToJson(this);
}
