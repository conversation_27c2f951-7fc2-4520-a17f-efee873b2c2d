// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'buyer_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BuyerInfo _$BuyerInfoFromJson(Map<String, dynamic> json) => BuyerInfo(
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      alias: json['alias'] as String?,
      emailAddress: json['email_address'] as String?,
      primaryPhoneNumber: json['primary_phone_number'] as String?,
      optionalPhoneNumber: json['optional_phone_number'] as String?,
      buyerLocationsOfInterest:
          (json['buyer_locations_of_interest'] as List<dynamic>?)
                  ?.map((e) => e as String)
                  .toList() ??
              const [],
      budget: json['budget'] as String?,
      bedroomCount: (json['bedroom_count'] as num?)?.toDouble(),
      bathroomCount: (json['bathroom_count'] as num?)?.toDouble(),
      area: (json['area'] as num?)?.toDouble(),
      additionalRequests: (json['additional_requests'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      buyerExpirationDate: json['buyer_expiration_date'] == null
          ? null
          : DateTime.parse(json['buyer_expiration_date'] as String),
      timeOffset: (json['time_offset'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BuyerInfoToJson(BuyerInfo instance) => <String, dynamic>{
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'alias': instance.alias,
      'time_offset': instance.timeOffset,
      'email_address': instance.emailAddress,
      'primary_phone_number': instance.primaryPhoneNumber,
      'optional_phone_number': instance.optionalPhoneNumber,
      'buyer_locations_of_interest': instance.buyerLocationsOfInterest,
      'budget': instance.budget,
      'bedroom_count': instance.bedroomCount,
      'bathroom_count': instance.bathroomCount,
      'area': instance.area,
      'additional_requests': instance.additionalRequests,
      'buyer_expiration_date': instance.buyerExpirationDate?.toIso8601String(),
    };
