import 'package:dio/dio.dart';
import '../../utils/common_utils.dart';

class NetworkConnectionInterceptor extends Interceptor {
  NetworkConnectionInterceptor();

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    /// Throw network exception in case of no connection
    if (await CommonUtils.hasNetwork()) {
      handler.next(options);
    } else {}
  }
}
