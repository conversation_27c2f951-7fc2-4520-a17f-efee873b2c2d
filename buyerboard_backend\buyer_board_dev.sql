--
-- PostgreSQL database dump
--

-- Dumped from database version 14.8 (Homebrew)
-- Dumped by pg_dump version 14.8 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: citext; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS citext WITH SCHEMA public;


--
-- Name: EXTENSION citext; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION citext IS 'data type for case-insensitive character strings';


--
-- Name: buyer_financial_status; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.buyer_financial_status AS ENUM (
    'pre_qualified',
    'pre_approved',
    'all_cash',
    'undetermined'
);


ALTER TYPE public.buyer_financial_status OWNER TO postgres;

--
-- Name: buyer_property_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.buyer_property_type AS ENUM (
    'single_family_house',
    'townhouse',
    'condo',
    'apartment',
    'multi_family_house',
    'mobile',
    'land',
    'fixer'
);


ALTER TYPE public.buyer_property_type OWNER TO postgres;

--
-- Name: buyer_purchase_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.buyer_purchase_type AS ENUM (
    'buy',
    'rent'
);


ALTER TYPE public.buyer_purchase_type OWNER TO postgres;

--
-- Name: buyer_rental_timeline; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.buyer_rental_timeline AS ENUM (
    'asap',
    'three_months',
    'six_months',
    'one_year_plus'
);


ALTER TYPE public.buyer_rental_timeline OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: buyer_needs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.buyer_needs (
    id bigint NOT NULL,
    purchase_type public.buyer_purchase_type,
    property_type public.buyer_property_type,
    financial_status public.buyer_financial_status,
    timeline public.buyer_rental_timeline,
    budget_upto character varying(255),
    min_bedrooms character varying(255),
    min_bathrooms character varying(255),
    min_area character varying(255),
    buyer_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE public.buyer_needs OWNER TO postgres;

--
-- Name: buyer_needs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.buyer_needs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.buyer_needs_id_seq OWNER TO postgres;

--
-- Name: buyer_needs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.buyer_needs_id_seq OWNED BY public.buyer_needs.id;


--
-- Name: buyers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.buyers (
    id bigint NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    image_url character varying(255),
    buyers_alias character varying(255),
    email character varying(255),
    primary_phone_number character varying(255),
    optional_phone_number character varying(255),
    buyer_locations character varying(255)[],
    additional_desires character varying(255)[],
    agreement_expiry_date timestamp(0) without time zone,
    user_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    is_favourite boolean DEFAULT false
);


ALTER TABLE public.buyers OWNER TO postgres;

--
-- Name: buyers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.buyers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.buyers_id_seq OWNER TO postgres;

--
-- Name: buyers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.buyers_id_seq OWNED BY public.buyers.id;


--
-- Name: images; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.images (
    id bigint NOT NULL,
    image character varying(255),
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE public.images OWNER TO postgres;

--
-- Name: images_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.images_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.images_id_seq OWNER TO postgres;

--
-- Name: images_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.images_id_seq OWNED BY public.images.id;


--
-- Name: organizations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.organizations (
    id bigint NOT NULL,
    company_name character varying(255),
    real_estate_lisence_no character varying(255),
    broker_lisence_no character varying(255),
    state character varying(255),
    zip_codes integer[],
    search_range character varying(255),
    user_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE public.organizations OWNER TO postgres;

--
-- Name: organizations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.organizations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.organizations_id_seq OWNER TO postgres;

--
-- Name: organizations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.organizations_id_seq OWNED BY public.organizations.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE public.schema_migrations OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    email public.citext NOT NULL,
    hashed_password character varying(255) NOT NULL,
    confirmed_at timestamp(0) without time zone,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    phone_number_primary character varying(255),
    phone_number_optional character varying(255),
    provider character varying(255) DEFAULT 'password'::character varying,
    image_url character varying(255),
    first_name character varying(255),
    last_name character varying(255),
    brokerage_name character varying(255),
    brokerage_lisence_no character varying(255),
    broker_street_address character varying(255),
    broker_city character varying(255),
    brokerage_zip_code character varying(255),
    brokerage_state character varying(255),
    is_completed boolean DEFAULT false
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: users_tokens; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users_tokens (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    token bytea NOT NULL,
    context character varying(255) NOT NULL,
    sent_to character varying(255),
    inserted_at timestamp(0) without time zone NOT NULL,
    otp integer
);


ALTER TABLE public.users_tokens OWNER TO postgres;

--
-- Name: users_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_tokens_id_seq OWNER TO postgres;

--
-- Name: users_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_tokens_id_seq OWNED BY public.users_tokens.id;


--
-- Name: buyer_needs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.buyer_needs ALTER COLUMN id SET DEFAULT nextval('public.buyer_needs_id_seq'::regclass);


--
-- Name: buyers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.buyers ALTER COLUMN id SET DEFAULT nextval('public.buyers_id_seq'::regclass);


--
-- Name: images id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.images ALTER COLUMN id SET DEFAULT nextval('public.images_id_seq'::regclass);


--
-- Name: organizations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.organizations ALTER COLUMN id SET DEFAULT nextval('public.organizations_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: users_tokens id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users_tokens ALTER COLUMN id SET DEFAULT nextval('public.users_tokens_id_seq'::regclass);


--
-- Data for Name: buyer_needs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.buyer_needs (id, purchase_type, property_type, financial_status, timeline, budget_upto, min_bedrooms, min_bathrooms, min_area, buyer_id, inserted_at, updated_at) FROM stdin;
1	buy	single_family_house	pre_qualified	three_months	900k	2	1.5	1.5k	1	2024-06-07 19:53:34	2024-06-07 19:53:34
2	buy	single_family_house	pre_qualified	three_months	900k	2	1.5	1.5k	2	2024-06-11 10:04:22	2024-06-11 10:04:22
3	buy	single_family_house	pre_qualified	three_months	900k	2	1.5	1.5k	3	2024-06-11 20:29:24	2024-06-11 20:29:24
4	buy	single_family_house	pre_qualified	three_months	900k	2	1.5	1.5k	4	2024-06-11 20:30:54	2024-06-11 20:30:54
5	buy	single_family_house	pre_qualified	three_months	900k	2	1.5	1.5k	5	2024-06-21 00:19:34	2024-06-21 00:19:34
\.


--
-- Data for Name: buyers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.buyers (id, first_name, last_name, image_url, buyers_alias, email, primary_phone_number, optional_phone_number, buyer_locations, additional_desires, agreement_expiry_date, user_id, inserted_at, updated_at, is_favourite) FROM stdin;
2	First	Last	/image.png	Alias	<EMAIL>	+12345	+123456	{Location1,Location2,Location3}	{Desire1,Desire2,Desire3}	2024-06-04 11:08:48	23	2024-06-11 10:04:22	2024-06-11 10:04:22	f
3	First	Last	/image.png	Alias	<EMAIL>	+12345	+123456	{Location1,Location2,Location3}	{Desire1,Desire2,Desire3}	2024-06-04 11:08:48	23	2024-06-11 20:29:24	2024-06-11 20:29:24	f
4	First	Last	/image.png	Alias	<EMAIL>	+12345	+123456	{Location1,Location2,Location3}	{Desire1,Desire2,Desire3}	2024-06-04 11:08:48	23	2024-06-11 20:30:54	2024-06-11 20:30:54	f
5	First	Last	/image.png	Alias	<EMAIL>	+12345	+123456	{Location1,Location2,Location3}	{Desire1,Desire2,Desire3}	2024-06-04 11:08:48	36	2024-06-21 00:19:34	2024-06-21 00:19:34	t
1	First	Last	/image.png	Alias	<EMAIL>	+12345	+123456	{Location1,Location2,Location3}	{Desire1,Desire2,Desire3}	2024-06-04 11:08:48	23	2024-06-07 19:53:34	2024-06-21 00:28:55	t
\.


--
-- Data for Name: images; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.images (id, image, inserted_at, updated_at) FROM stdin;
1	Screenshot 2024-06-13 at 12.11.50 AM.png?63886133299	2024-06-20 20:08:19	2024-06-20 20:08:19
\.


--
-- Data for Name: organizations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.organizations (id, company_name, real_estate_lisence_no, broker_lisence_no, state, zip_codes, search_range, user_id, inserted_at, updated_at) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.schema_migrations (version, inserted_at) FROM stdin;
20240515212126	2024-05-15 21:32:13
20240521213018	2024-05-21 21:32:22
20240523223804	2024-05-24 21:36:28
20240524213017	2024-05-24 21:38:03
20240527210128	2024-05-27 21:06:25
20240530210547	2024-05-30 21:07:23
20240530211901	2024-05-30 21:23:43
20240603192834	2024-06-03 19:34:39
20240603193035	2024-06-03 19:34:39
20240603193449	2024-06-04 09:51:09
20240604090542	2024-06-04 11:14:36
20240607192035	2024-06-07 19:38:35
20240607193430	2024-06-07 19:46:10
20240607195112	2024-06-07 19:53:31
20240611083503	2024-06-11 08:54:26
20240613184052	2024-06-13 18:41:22
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, email, hashed_password, confirmed_at, inserted_at, updated_at, phone_number_primary, phone_number_optional, provider, image_url, first_name, last_name, brokerage_name, brokerage_lisence_no, broker_street_address, broker_city, brokerage_zip_code, brokerage_state, is_completed) FROM stdin;
23	<EMAIL>	$2b$12$.w1s6NziUzFtIbGNr4z1GecNSsb/IU11mABppQrclvoeVoDrmA9XW	\N	2024-05-20 19:28:36	2024-06-11 09:18:46	+012345789	+012345789	password	/uploads/9C96BFEA-4192-4396-AC69-41234EE55236_1_201_a.png	User's first Name	User's last Name	Broker Name	LIS1234	123, Hope Street	New York	12345	CA	t
35	<EMAIL>	$2b$12$MsRiXTKdhbwU9F5UHX8xmuxBUX1ZwBkN0MdmINbUj50eM0.97FlR6	\N	2024-06-11 10:04:45	2024-06-11 10:04:45	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
36	<EMAIL>	$2b$12$ESqI3FvAXVEh.ub3tAuBKezFIpIpKw9oAOMDGiICVwIZBtHT93iBC	\N	2024-06-21 00:19:19	2024-06-21 00:19:19	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
24	<EMAIL>	$2b$12$kCLIDFYWuj7wSAQKESQkeOyOD0IwmOCjbE.ExDHQ.b1f7LEpnFKXK	\N	2024-05-20 19:42:10	2024-05-20 19:42:10	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
25	<EMAIL>	$2b$12$BtMuxLd/cBp8CWma5cxGi.dTdjCpwSU.4mm1OTGHSpte5zz93NmqS	\N	2024-05-20 19:52:22	2024-05-20 19:52:22	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
26	<EMAIL>	$2b$12$xzVN8p9own/R6BijKKqRXuOgWjXCrRcWavf6S6j5eZ3eXHHAt421u	\N	2024-05-20 20:20:37	2024-05-20 20:20:37	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
27	<EMAIL>	$2b$12$ugJvYdV43BzFj4q2UkrHVeSGhqulOlp9Blh.eIoZiDO7Ri.K3mZb.	\N	2024-05-20 20:25:37	2024-05-20 20:25:37	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
28	<EMAIL>	$2b$12$D9QE1X83BCUa1y95DKeqdeh4K.XS/Atycb3vPew1kvLULQhm11fF.	\N	2024-05-20 20:25:43	2024-05-20 20:25:43	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
29	<EMAIL>	$2b$12$wAAl4Aq67al50/5Ue4IDk.BfMiO4.YMAP91LV.zEtMOn8kLXK/JXa	\N	2024-05-21 18:27:52	2024-05-21 18:27:52	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
30	<EMAIL>	$2b$12$xkQf702oRZBKkVf6vrWMIOMyrnp081QRW.VZtmtXXOCJc1z36DA32	\N	2024-05-24 22:30:16	2024-05-24 22:30:16	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
33	<EMAIL>	$2b$12$Y60dG8krxh/tzc4FT1uLg.OX8XfHJwNRcPzBAdg2txG5Qzsf/3JGS	\N	2024-05-27 21:38:06	2024-05-27 21:38:06	\N	\N	google	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
34	<EMAIL>	$2b$12$L3aIGkfS8I55zKJ6crFchOLbGF84P9BzwUoHiRFIBmyRi74zFaOlC	\N	2024-05-28 22:29:53	2024-05-28 22:29:53	\N	\N	password	\N	\N	\N	\N	\N	\N	\N	\N	\N	f
\.


--
-- Data for Name: users_tokens; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users_tokens (id, user_id, token, context, sent_to, inserted_at, otp) FROM stdin;
120	34	\\x3f9914f0ba1de72a3729acd5975b6f3f2cf9ab1fd1b7d25e8427ad5690e68f4f	reset_password_request	<EMAIL>	2024-05-28 23:22:35	695826
67	29	\\xab445e074d1e503a2f817e613e8e752cc2dab9de5c387064cd700cdd1254a7bd	confirm	<EMAIL>	2024-05-21 18:27:52	\N
68	29	\\xc77a9924eeaa8c57f8c7e706b07f2a54ce2de4837d8792cbd194f6f308fad609	session	\N	2024-05-21 18:27:52	\N
123	23	\\x2222e49d485b5aa4d3b245531a6c0ca7bc31af5e0b6e6c6a916b347f15f50cb5	session	\N	2024-05-30 21:29:13	\N
124	23	\\x636eb238b9b5a58cae65679df6af45fac5ea5c25b1a1bb5749390ae369c45e92	session	\N	2024-05-30 21:30:25	\N
125	23	\\x83cc539e76b8730fd9725879e972737229fa69fb91f506c41e2973da4cf97209	session	\N	2024-06-04 08:41:51	\N
127	23	\\x5db4b4e2d5bbf24e22fbd1f9ffdc5a148ab4b152c3f7c9f926a0e9d37f958b0c	verified	<EMAIL>	2024-06-04 08:42:45	677801
128	23	\\x0f2bc89056ece1db543586e47a6860e072c96406068a6e5c4d674421ecc60e97	session	\N	2024-06-04 11:20:09	\N
129	23	\\x48c83e458e133ef6c4c2727b5a27df3bb8c956ee4bb54b8f0aa3d9928531a70c	session	\N	2024-06-04 11:22:08	\N
130	23	\\x64412255bf9813578f0ae29a3c84e49c99fcfab10f2866e66b01ed5853a2331b	session	\N	2024-06-04 11:22:16	\N
131	23	\\x6db4d4ee42d9aac115ddecf9b71a0523bbfc1fd788646e8b20fe19d1ab61e317	session	\N	2024-06-04 11:40:25	\N
132	23	\\x6a052b5df0f920fa981fae07a45d909296a3ebe8d0c756499fad513d30e31761	session	\N	2024-06-04 12:24:43	\N
133	23	\\xd77a5c0e1ca3da5381455b1ffa100986bd8961bcdbd24a7c0bfb33d311176f24	session	\N	2024-06-04 12:53:29	\N
134	23	\\x55ae8a1215de285da528d52e9e3111ba41ea30e45e3b786b118eab43e0c06d5f	session	\N	2024-06-05 21:34:10	\N
135	23	\\x67c0a667b308fad35a5e5bd080a10002257fc3d4361e73c1ffb98b7576cc170a	session	\N	2024-06-05 21:37:46	\N
136	23	\\x73e37a1f05ef6af562eb7a103741f6dd1e7b59d74ec9f0cdc14b6c973ef459af	session	\N	2024-06-07 17:06:49	\N
137	23	\\x2379f7117912e80a8813a4339df86a99f0843f8a143a3b517d7d2887775ce309	session	\N	2024-06-07 19:30:36	\N
138	23	\\x405029e1e703417fe2de9191e6da22fd77b31b8ff243771aae49815095520055	session	\N	2024-06-07 19:49:51	\N
139	23	\\x529e6bfdda53c9dc4c17e85b191b26b8d44b4ae7933cf81a55d3ea94d6190026	session	\N	2024-06-11 08:55:53	\N
140	23	\\x1ed7ebb35bc05416254c0218bddb1233b3524725432b225cf4aa4ed4c473bfe7	session	\N	2024-06-11 08:58:23	\N
141	23	\\xc2a1b188fe5173876da0d5687a188709fbbecb6536461d7e01c8b0ab6a69f254	session	\N	2024-06-11 09:27:27	\N
142	23	\\xc7ae4fcf43f7e6fc9a0f4aa8309c97e0715eb93a912151ad79d2ba7e08f018c1	session	\N	2024-06-11 09:58:54	\N
143	23	\\x38785e99a957e2d55c793cd0f75289c597a4840feb591c6aba22f6a4138c61a1	session	\N	2024-06-11 10:02:43	\N
144	23	\\x88cd6f0dd2a6b2c207ebcab42d998082822d52f90173d4828949acfa3d8cf78d	session	\N	2024-06-11 10:03:38	\N
145	35	\\x3abbf8ef5f6cc202aa6a964a6b010c7540a1fc02c525910fbb9f53d993ee6d6a	session	\N	2024-06-11 10:04:45	\N
146	23	\\x5d455d6959a6c17b2ad818e11779ea3a9fb8939aa5344dc6677963f957a9fcfd	session	\N	2024-06-11 20:21:04	\N
147	23	\\x147ceb0749d2b11dc388e7c28437c48dec80edac7f870b30baa0aefc99c76a12	session	\N	2024-06-11 20:30:08	\N
148	23	\\x3f315aab55537c0e027bec9e99936617cd8a66f7bc3a5a58055bfc51fdc2037e	session	\N	2024-06-11 20:38:21	\N
149	23	\\x4da8e852954afcdcbbb3d8fcd92012c04ef2e9ee4949373adb68c5ed5e885165	session	\N	2024-06-12 10:45:11	\N
150	23	\\x9cf853020f71a9dc2170dedabc0830bb081f53004b14049011d4b4feb0421ee9	session	\N	2024-06-13 18:33:02	\N
151	23	\\x2d49f95261ff0aa3da2e711f31bab0dbeb1a24afeb4d3ca5fc96b770be4cf0fe	session	\N	2024-06-14 20:02:39	\N
152	23	\\x0d8eba5e38d8cb98df4030b934512bd0f3da7ee15f397057ae00c29b8e6a2841	session	\N	2024-06-14 20:05:21	\N
153	23	\\xe9283120b07567e08ba040f0cbef13549fc2029cefc6580c24e1f84a97d04593	session	\N	2024-06-14 20:15:24	\N
154	23	\\x28f5a57c73d3653fb152c32a939e4ff48df782172f92f21daf59974de43c7eee	session	\N	2024-06-20 20:11:43	\N
155	23	\\xed8ca7187d6cbf2d69954c6668386a00873e7ac1fba251c7d55afad76771f246	session	\N	2024-06-21 00:09:31	\N
156	36	\\x66dba948c6cbb4fc9b99c3dcd282c31b3f5f0a2be32b0871be22bbcc037cdd6d	session	\N	2024-06-21 00:19:19	\N
100	30	\\x0f616048986ff2def0673f30cd4c631254c05c74ec086437c71b84f5aa68642d	session	\N	2024-05-24 22:30:16	\N
101	23	\\x84df82f1ef2ddd6398f32b99e139c5f7cb7cf6efb8fbb8922b07d9662a594846	session	\N	2024-05-24 22:49:59	\N
102	23	\\xbb894d5982db911e783846b48eeebb58816013d4ee23f83534741ebe8ab03ff5	session	\N	2024-05-25 00:19:22	\N
103	23	\\xe28935be14a528014a3fb093d711b174f604df7f3b436d3dc92448577e7b13ab	session	\N	2024-05-25 00:22:29	\N
104	23	\\x967cadc1e63328f214f4c6347da83cc91fbea9990e8c0dda2ba50cb1de85c3e8	session	\N	2024-05-27 21:18:17	\N
105	23	\\xfe073ab075a9cd57299bce4abb444f4c3412fba4d3de2bb8c8580e08ddeaffc1	session	\N	2024-05-27 21:21:54	\N
106	33	\\x56776a102848fdb94acf720485480769362e2a5a2bd75af2793690ddda2454e7	session	\N	2024-05-27 21:38:06	\N
107	33	\\x90b0bac2afcc977a59c86f226a7c3e8068a8a63ed26718c87dae0c8836d30309	session	\N	2024-05-27 21:38:44	\N
108	33	\\x0e93a8f554da1453be8757f2418eeca186939e77e9e66de8fe4bba601cdf22af	session	\N	2024-05-27 21:38:50	\N
110	34	\\xb57738617c208a454aa5393a5e0a6ccb50152fd36115020df309123325ad9242	session	\N	2024-05-28 22:29:53	\N
\.


--
-- Name: buyer_needs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.buyer_needs_id_seq', 5, true);


--
-- Name: buyers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.buyers_id_seq', 5, true);


--
-- Name: images_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.images_id_seq', 1, true);


--
-- Name: organizations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.organizations_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 36, true);


--
-- Name: users_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_tokens_id_seq', 156, true);


--
-- Name: buyer_needs buyer_needs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.buyer_needs
    ADD CONSTRAINT buyer_needs_pkey PRIMARY KEY (id);


--
-- Name: buyers buyers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.buyers
    ADD CONSTRAINT buyers_pkey PRIMARY KEY (id);


--
-- Name: images images_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.images
    ADD CONSTRAINT images_pkey PRIMARY KEY (id);


--
-- Name: organizations organizations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users_tokens users_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users_tokens
    ADD CONSTRAINT users_tokens_pkey PRIMARY KEY (id);


--
-- Name: users_email_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX users_email_index ON public.users USING btree (email);


--
-- Name: users_tokens_context_token_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX users_tokens_context_token_index ON public.users_tokens USING btree (context, token);


--
-- Name: users_tokens_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX users_tokens_user_id_index ON public.users_tokens USING btree (user_id);


--
-- Name: buyer_needs buyer_needs_buyer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.buyer_needs
    ADD CONSTRAINT buyer_needs_buyer_id_fkey FOREIGN KEY (buyer_id) REFERENCES public.buyers(id) ON DELETE CASCADE;


--
-- Name: buyers buyers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.buyers
    ADD CONSTRAINT buyers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: organizations organizations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users_tokens users_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users_tokens
    ADD CONSTRAINT users_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

