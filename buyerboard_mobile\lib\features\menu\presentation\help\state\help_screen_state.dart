import 'package:buyer_board/features/menu/data/models/help_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'help_screen_state.freezed.dart';

@freezed
class HelpScreenState with _$HelpScreenState {
  const factory HelpScreenState.initial() = initial;
  const factory HelpScreenState.loading() = loading;
  const factory HelpScreenState.success(List<HelpResponse> helpResponse) = success;
  const factory HelpScreenState.helpScreenError(String? error) =
  helpScreenError;
}