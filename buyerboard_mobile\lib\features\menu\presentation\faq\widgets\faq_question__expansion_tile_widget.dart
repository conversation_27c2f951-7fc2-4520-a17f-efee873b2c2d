import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/menu/data/models/faq_model.dart';
import 'package:buyer_board/features/menu/presentation/help/widgets/like_and_unlike_buttons_widget.dart';
import 'package:flutter/material.dart';

class FaqQuestionExpansionTileWidget extends StatefulWidget {
  const FaqQuestionExpansionTileWidget({super.key, required this.question});
  final FaqQuestionData question;

  @override
  State<FaqQuestionExpansionTileWidget> createState() =>
      _FaqQuestionExpansionTileWidgetState();
}

class _FaqQuestionExpansionTileWidgetState
    extends State<FaqQuestionExpansionTileWidget> {
  bool _isExpanded = false;
  @override
  Widget build(BuildContext context) {
    return ListTileTheme(
      dense: true,
      child: ExpansionTile(
        // shape: const Border(),
        onExpansionChanged: (value) {
          setState(() {
            _isExpanded = value;
          });
        },
        trailing: AnimatedRotation(
          turns: _isExpanded ? 0.5 : 0,
          duration: const Duration(milliseconds: 200),
          child: Icon(
            Icons.arrow_drop_down,
            size: 34,
            color: _isExpanded
                ? context.appColors.pPXLight
                : context.appColors.greyM,
          ),
        ),
        childrenPadding: const EdgeInsets.only(bottom: 14),
        title: Text(
          widget.question.title,
          style: context.typography.mediumSemi,
        ),
        children: [
          Text(
            widget.question.description,
            textAlign: TextAlign.justify,
            style: context.typography.mediumReg.copyWith(
              color: context.appColors.greyDefaultGreyMedium,
            ),
          ),
          spacerH12,
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Was this helpful',
                style: context.typography.mediumSemi,
              ),
              const LikeAndUnlikeButtonsWidget(),
            ],
          ),
        ],
      ),
    );
  }
}
