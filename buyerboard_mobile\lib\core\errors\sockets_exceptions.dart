// socket_exception.dart

abstract class SocketException implements Exception {
  String get message;

  @override
  String toString() => message;
}

class ConnectionException extends SocketException {
  final String details;

  ConnectionException([this.details = '']);

  @override
  String get message =>
      'Connection error${details.isNotEmpty ? ': $details' : ''}';
}

class AuthenticationException extends SocketException {
  final String details;

  AuthenticationException([this.details = '']);

  @override
  String get message =>
      'Authentication error${details.isNotEmpty ? ': $details' : ''}';
}

class MessageSendException extends SocketException {
  final String details;

  MessageSendException([this.details = '']);

  @override
  String get message =>
      'Message sending error${details.isNotEmpty ? ': $details' : ''}';
}

class DisconnectionException extends SocketException {
  final String details;

  DisconnectionException([this.details = '']);

  @override
  String get message =>
      'Disconnection error${details.isNotEmpty ? ': $details' : ''}';
}

class UnexpectedException extends SocketException {
  final String details;

  UnexpectedException([this.details = '']);

  @override
  String get message =>
      'Unexpected error${details.isNotEmpty ? ': $details' : ''}';
}
