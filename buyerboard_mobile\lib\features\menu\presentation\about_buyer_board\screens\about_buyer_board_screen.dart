import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/features/home/<USER>/cubit/home_tab_cubit.dart';
import 'package:buyer_board/features/intro/presentation/screens/app_onboarding_screen.dart';
import 'package:buyer_board/features/menu/presentation/widgets/menu_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../cubit/intro_to_buyer_board_cubit.dart';

class AboutBuyerBoardScreen extends StatefulWidget {
  const AboutBuyerBoardScreen({super.key});

  @override
  State<AboutBuyerBoardScreen> createState() => _AboutBuyerBoardScreenState();
}

class _AboutBuyerBoardScreenState extends State<AboutBuyerBoardScreen> {
  @override
  void initState() {
    context.read<IntroToBuyerBoardCubit>().initBuyerBoardIntro();
    super.initState();
  }

  final termsOfServiceLink = 'https://buyerboard.com/terms';
  final privacyPolicyLink = 'https://buyerboard.com/privacy';

  // This function will open the Terms of Service link
  Future<void> _goToTermsOfService(BuildContext context) async {
    final uri = Uri.parse(termsOfServiceLink);
    CoreUtils.launchUri(context: context, uri: uri);
    // context.push(PagePath.termsOfService);
  }

  // This function will open the Privacy Policy link
  Future<void> _goToPrivacyPolicy(BuildContext context) async {
    final uri = Uri.parse(privacyPolicyLink);
    CoreUtils.launchUri(context: context, uri: uri);
    // context.push(PagePath.privacyPolicy);
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Scaffold(
      appBar: ApplicationAppBar.buildAppBar(
        context,
        title: Strings.aboutBuyerBoard,
        leadingWidget: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: colors.onPrimaryFixed,
              size: Dimensions.padding_28,
            ),
            color: colors.surface,
            onPressed: () {
              context.shouldPop();
            }),
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          MenuItem(
              title: Strings.introToBuyerBoard,
              leadingIcon: SvgPicture.asset(
                Drawables.bbLogo,
                height: Dimensions.padding_24,
                color: colors.primary,
                width: Dimensions.padding_24,
              ),
              trailingIcon: Icon(
                Icons.arrow_forward_ios,
                size: Dimensions.materialPadding,
                color: colors.onSurfaceVariant,
              ),
              onTapMenuItem: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => AppOnboardingScreen(
                        onSkip: () => context.pop(),
                        onDone: (shouldCreateCard) {
                          if (shouldCreateCard) {
                            context
                                .read<HomeBottomNarBarTabCubit>()
                                .changeTab(2);
                            context.go(PagePath.mainScreen);
                          } else {
                            context.pop();
                          }
                        },
                      ),
                    ),
                  )),
          MenuItem(
            title: Strings.termsOfService,
            leadingIcon: SvgPicture.asset(
              Drawables.icFaq,
              height: Dimensions.padding_24,
              color: colors.primary,
              width: Dimensions.padding_24,
            ),
            // onTapMenuItem: () => context.push(PagePath.termsOfService)
            onTapMenuItem: () => _goToTermsOfService(context),
            trailingIcon: Icon(
              Icons.arrow_forward_ios,
              size: Dimensions.materialPadding,
              color: colors.onSurfaceVariant,
            ),
          ),
          MenuItem(
            title: Strings.privacyPolicy,
            leadingIcon: Icon(
              Icons.help,
              color: colors.primary,
              size: Dimensions.padding_24,
            ),
            // onTapMenuItem: () => context.push(PagePath.privacyPolicy),
            onTapMenuItem: () => _goToPrivacyPolicy(context),
            trailingIcon: Icon(
              Icons.arrow_forward_ios,
              size: Dimensions.materialPadding,
              color: colors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
