import 'package:buyer_board/common/widgets/profile_text_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/utils/text_field_formatters.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/buyer_info_cubit.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/financial_status_widget.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/property_attribute.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/property_type_widget.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/purchase_type_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../core/resources/resources.dart';

class BuyerNeedsSection extends StatelessWidget {
  const BuyerNeedsSection({
    super.key,
    required this.minimumBudgetController,
    required this.minimumBedRoomsController,
    required this.minimumBathRoomsController,
    required this.minimumSquareFootageController,
  });

  final CurrencyTextEditingController minimumBudgetController;
  final TextEditingController minimumBedRoomsController;
  final TextEditingController minimumBathRoomsController;
  final CurrencyTextEditingController minimumSquareFootageController;
  void onChangeMinimumBudget(BuildContext context, String? budget) =>
      context.read<BuyerInfoCubit>().setBudget(budget);
  void onChangeBedroomCount(BuildContext context, double? bedroomCount) =>
      context.read<BuyerInfoCubit>().setBedroomCount(bedroomCount);
  void onChangeBathroomCount(BuildContext context, double? bathRoomCount) =>
      context.read<BuyerInfoCubit>().setBathroomCount(bathRoomCount);
  void onChangeArea(BuildContext context, double? area) =>
      context.read<BuyerInfoCubit>().setArea(area);
  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.buyerNeeds,
          style: context.typography.large1xBlack,
        ),
        Text(
          Strings.buyerNeedsDesc,
          style: context.typography.mediumReg,
        ),
        const SizedBox(height: 16),
        const PurchaseTypeWidget(),
        const SizedBox(height: 16),
        const PropertyTypeWidget(),
        const SizedBox(height: 16),
        const FinancialStatusWidget(),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: minimumBudgetController,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(10),
            USCurrencyInputFormatter(),
            FilteringTextInputFormatter.deny(
              RegExp(r'^0+'),
            ),
          ],
          onChanged: (budget) => onChangeMinimumBudget(
              context, minimumBudgetController.numericValue.toString()),
          keyboardType: const TextInputType.numberWithOptions(),
          validator: Validators().requiredFieldValidator,
          label: Strings.budgetAmount,
          hint: Strings.value,
        ),
        const SizedBox(height: 32),
        Row(
          children: [
            PropertyAttribute(
              onChanged: (bedroomCount) {
                double parsedValue =
                    double.tryParse(bedroomCount ?? "0") ?? 0.0;
                onChangeBedroomCount(context, parsedValue);
              },
              attributeController: minimumBedRoomsController,
              label: Strings.bedroomsMin,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(3),
                FilteringTextInputFormatter.deny(
                  RegExp(r'^0+'),
                ),
              ],
              icon: SvgPicture.asset(
                Drawables.icBedroomOutlined,
                colorFilter: ColorFilter.mode(
                  colors.onPrimary,
                  BlendMode.srcIn,
                ),
                width: 24,
                height: 24,
              ),
            ),
            const SizedBox(width: 12),
            PropertyAttribute(
              onChanged: (bathroomCount) {
                double parsedValue =
                    double.tryParse(bathroomCount ?? "0") ?? 0.0;
                onChangeBathroomCount(context, parsedValue);
              },
              attributeController: minimumBathRoomsController,
              label: Strings.bathroomsMin,
              inputFormatters: [
                LengthLimitingTextInputFormatter(3),
                FilteringTextInputFormatter.allow(
                  RegExp(r'^(?!0)\d*\.?\d*'),
                ),
              ],
              icon: SvgPicture.asset(
                Drawables.icBathroomOutlined,
                colorFilter: ColorFilter.mode(
                  colors.onPrimary,
                  BlendMode.srcIn,
                ),
                width: 24,
                height: 24,
              ),
            ),
            const SizedBox(width: 12),
            PropertyAttribute(
              onChanged: (area) => onChangeArea(
                  context, minimumSquareFootageController.numericValue),
              attributeController: minimumSquareFootageController,
              label: Strings.sqFootageMin,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(8),
                USCurrencyInputFormatter(symbol: ''),
                FilteringTextInputFormatter.deny(
                  RegExp(r'^0+'),
                ),
              ],
              icon: SvgPicture.asset(
                Drawables.icAreaOutlined,
                colorFilter: ColorFilter.mode(
                  colors.onPrimary,
                  BlendMode.srcIn,
                ),
                width: 24,
                height: 24,
              ),
            ),
          ],
        ),
        const SizedBox(height: 32),
      ],
    );
  }
}
