import 'package:json_annotation/json_annotation.dart';
part 'help_response.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class HelpResponse {
  const HelpResponse({
    required this.title,
    required this.description,
    required this.category,
    required this.topics,
  });
  final String title;
  final String description;
  final List<HelpCategory> category;
  final List<Topic> topics;

  factory HelpResponse.fromJson(Map<String, dynamic> json) =>
      _$HelpResponseFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class HelpCategory {
  HelpCategory({required this.description, required this.title});
  final String title;
  final String description;
  factory HelpCategory.fromJson(Map<String, dynamic> json) =>
      _$HelpCategoryFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class Topic {
  Topic({required this.description, required this.title});
  final String title;
  final String description;
  factory Topic.fromJson(Map<String, dynamic> json) => _$Top<PERSON>(json);
}
