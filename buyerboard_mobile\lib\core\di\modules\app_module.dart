import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/network/google_auth_service/google_auth_service.dart';
import 'package:buyer_board/core/utils/local_auth_util.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:kiwi/kiwi.dart';
import 'package:get_storage/get_storage.dart';

abstract class AppModule {
  static late KiwiContainer _container;

  static Future<void> setup(
      {required KiwiContainer container, required AppConfig appConfig}) async {
    _container = container;
    // await _setupFirebase();
    _setupGoogleAuthService();
    await _setupStorage();
    _setupAppPreferences();
    _setupLocalAuth();
  }

  // static Future<void> _setupFirebase() async {
  //   if (AppConfig.environment != Environment.dev) {
  //     // await Firebase.initializeApp();
  //     _setupFirebaseCrashlytics();
  //   }
  // }

  // static void _setupStripe(String publishableKey) {
  //   Stripe.publishableKey = publishableKey;
  // }

  static void _setupGoogleAuthService() =>
      _container.registerSingleton((_) => GoogleAuthService());

  static void _setupLocalAuth() =>
      _container.registerSingleton((_) => LocalAuthUtil());

  static Future<bool> _setupStorage() async {
    return await GetStorage.init();
  }

  static void _setupAppPreferences() {
    _container.registerSingleton((_) => AppPreferences());
  }
}
