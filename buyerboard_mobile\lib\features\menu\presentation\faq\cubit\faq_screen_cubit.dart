import 'dart:convert';

import 'package:buyer_board/features/menu/data/models/faq_model.dart';
import 'package:buyer_board/features/menu/presentation/faq/state/faq_screen_state.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FaqScreenCubit extends Cubit<FaqScreenState> {
  FaqScreenCubit() : super(const FaqScreenState.initial());

  Future<void> getFaqs() async {
    emit(const FaqScreenState.loading());
    try {
      final source = await rootBundle.loadString("assets/json/faq.json");
      final json = jsonDecode(source) ;
      final response =  FaqModel.fromJson(json) ;
      emit(FaqScreenState.success(response));
    } catch (e) {
      emit(FaqScreenState.faqError(e.toString()));
    }
  }
}
