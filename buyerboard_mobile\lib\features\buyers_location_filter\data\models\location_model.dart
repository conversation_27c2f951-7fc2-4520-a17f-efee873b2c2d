import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'location_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class LocationModel extends LocationEntity {
  const LocationModel({
    required super.zipCode,
    required super.cityName,
    required super.stateId,
    required super.stateName,
    required super.latitude,
    required super.longitude,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) =>
      _$LocationModelFromJson(json);

  Map<String, dynamic> toJson() => _$LocationModelToJson(this);
}
