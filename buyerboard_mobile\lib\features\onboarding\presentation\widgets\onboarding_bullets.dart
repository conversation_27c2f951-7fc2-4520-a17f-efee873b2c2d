import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/onboarding/presentation/widgets/onboarding_page_indicators.dart';
import 'package:flutter/material.dart';

class OnBoardingBullets extends StatelessWidget {
  const OnBoardingBullets(
      {super.key, required this.bullets, this.bulletPointColor});
  final List<String> bullets;
  final Color? bulletPointColor;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: bullets
          .map((element) => BulletedText(
              content: element, bulletPointColor: bulletPointColor))
          .toList(),
    );
  }
}

class BulletedText extends StatelessWidget {
  const BulletedText({super.key, required this.content, this.bulletPointColor});
  final String content;
  final Color? bulletPointColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        PageIndicator(
          size: 4,
          indicatorColor: bulletPointColor,
        ),
        const SizedBox(
          width: 4,
        ),
        Expanded(
          child: Text(
            content,
            style: AppStyles.medium
                .copyWith(color: bulletPointColor ?? AppColors.white),
          ),
        )
      ],
    );
  }
}
