import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// import '../../../data/sources/local/preferences/app_preferences.dart';
// import '../../di/injector.dart';
import '../network_keys.dart';

class AuthInterceptor extends Interceptor {
  AuthInterceptor();
  final _appPreferences = Injector.resolve<AppPreferences>();
  final _userSessionCubit = Injector.resolve<UserSessionCubit>();

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    bool isTokenRequired =
        options.headers.containsKey(NetworkKeys.requiresToken);
    if (isTokenRequired) {
      isTokenRequired = options.headers[NetworkKeys.requiresToken] as bool;
    }
    if (isTokenRequired) {
      final token =
          _appPreferences.getUser()?.token ?? _userSessionCubit.state?.token;
      if (token != null) {
        options.headers.remove(NetworkKeys.requiresToken);
        options.headers.addAll({
          NetworkKeys.authHeaderKey: token,
        });
      }
    }

    handler.next(options);
  }
}

class UserSessionCubit extends Cubit<User?> {
  UserSessionCubit() : super(null);

  void setUser(User user) => emit(user);
}
