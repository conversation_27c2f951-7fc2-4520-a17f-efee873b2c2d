import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:equatable/equatable.dart';

sealed class AllChatsEvent extends Equatable {
  const AllChatsEvent();
}

class GetAllChats extends AllChatsEvent {
  @override
  List<Object?> get props => [];
}

class ArchiveChat extends AllChatsEvent {
  final ChatPayload payload;
  const ArchiveChat(this.payload);

  @override
  List<Object> get props => [payload];
}

// class UnArchiveChat extends AllChatsEvent {
//   final ChatPayload payload;
//   const UnArchiveChat(this.payload);

//   @override
//   List<Object> get props => [payload];
// }

class DeleteChat extends AllChatsEvent {
  final ChatPayload payload;
  const DeleteChat(this.payload);

  @override
  List<Object> get props => [payload];
}

class CloseAllChatConnections extends AllChatsEvent {
  const CloseAllChatConnections();

  @override
  List<Object> get props => [];
}

class SeenChatEvent extends AllChatsEvent {
  const SeenChatEvent({required this.buyerId, required this.threadId});
  final int buyerId;
  final int threadId;

  @override
  List<Object> get props => [buyerId, threadId];
}
