// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reset_password_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ResetPasswordState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResetPasswordStateCopyWith<$Res> {
  factory $ResetPasswordStateCopyWith(
          ResetPasswordState value, $Res Function(ResetPasswordState) then) =
      _$ResetPasswordStateCopyWithImpl<$Res, ResetPasswordState>;
}

/// @nodoc
class _$ResetPasswordStateCopyWithImpl<$Res, $Val extends ResetPasswordState>
    implements $ResetPasswordStateCopyWith<$Res> {
  _$ResetPasswordStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'ResetPasswordState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements ResetPasswordState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'ResetPasswordState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements ResetPasswordState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$otpSentImplCopyWith<$Res> {
  factory _$$otpSentImplCopyWith(
          _$otpSentImpl value, $Res Function(_$otpSentImpl) then) =
      __$$otpSentImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$otpSentImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$otpSentImpl>
    implements _$$otpSentImplCopyWith<$Res> {
  __$$otpSentImplCopyWithImpl(
      _$otpSentImpl _value, $Res Function(_$otpSentImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$otpSentImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$otpSentImpl implements otpSent {
  const _$otpSentImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ResetPasswordState.otpSent(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$otpSentImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$otpSentImplCopyWith<_$otpSentImpl> get copyWith =>
      __$$otpSentImplCopyWithImpl<_$otpSentImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return otpSent(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return otpSent?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (otpSent != null) {
      return otpSent(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return otpSent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return otpSent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (otpSent != null) {
      return otpSent(this);
    }
    return orElse();
  }
}

abstract class otpSent implements ResetPasswordState {
  const factory otpSent(final String message) = _$otpSentImpl;

  String get message;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$otpSentImplCopyWith<_$otpSentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$wrongOtpImplCopyWith<$Res> {
  factory _$$wrongOtpImplCopyWith(
          _$wrongOtpImpl value, $Res Function(_$wrongOtpImpl) then) =
      __$$wrongOtpImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$wrongOtpImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$wrongOtpImpl>
    implements _$$wrongOtpImplCopyWith<$Res> {
  __$$wrongOtpImplCopyWithImpl(
      _$wrongOtpImpl _value, $Res Function(_$wrongOtpImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$wrongOtpImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$wrongOtpImpl implements wrongOtp {
  const _$wrongOtpImpl(this.error);

  @override
  final String error;

  @override
  String toString() {
    return 'ResetPasswordState.wrongOtp(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$wrongOtpImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$wrongOtpImplCopyWith<_$wrongOtpImpl> get copyWith =>
      __$$wrongOtpImplCopyWithImpl<_$wrongOtpImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return wrongOtp(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return wrongOtp?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (wrongOtp != null) {
      return wrongOtp(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return wrongOtp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return wrongOtp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (wrongOtp != null) {
      return wrongOtp(this);
    }
    return orElse();
  }
}

abstract class wrongOtp implements ResetPasswordState {
  const factory wrongOtp(final String error) = _$wrongOtpImpl;

  String get error;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$wrongOtpImplCopyWith<_$wrongOtpImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$otpVerifiedImplCopyWith<$Res> {
  factory _$$otpVerifiedImplCopyWith(
          _$otpVerifiedImpl value, $Res Function(_$otpVerifiedImpl) then) =
      __$$otpVerifiedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$otpVerifiedImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$otpVerifiedImpl>
    implements _$$otpVerifiedImplCopyWith<$Res> {
  __$$otpVerifiedImplCopyWithImpl(
      _$otpVerifiedImpl _value, $Res Function(_$otpVerifiedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$otpVerifiedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$otpVerifiedImpl implements otpVerified {
  const _$otpVerifiedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ResetPasswordState.otpVerified(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$otpVerifiedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$otpVerifiedImplCopyWith<_$otpVerifiedImpl> get copyWith =>
      __$$otpVerifiedImplCopyWithImpl<_$otpVerifiedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return otpVerified(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return otpVerified?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (otpVerified != null) {
      return otpVerified(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return otpVerified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return otpVerified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (otpVerified != null) {
      return otpVerified(this);
    }
    return orElse();
  }
}

abstract class otpVerified implements ResetPasswordState {
  const factory otpVerified(final String message) = _$otpVerifiedImpl;

  String get message;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$otpVerifiedImplCopyWith<_$otpVerifiedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$passwordUpdatedImplCopyWith<$Res> {
  factory _$$passwordUpdatedImplCopyWith(_$passwordUpdatedImpl value,
          $Res Function(_$passwordUpdatedImpl) then) =
      __$$passwordUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$passwordUpdatedImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$passwordUpdatedImpl>
    implements _$$passwordUpdatedImplCopyWith<$Res> {
  __$$passwordUpdatedImplCopyWithImpl(
      _$passwordUpdatedImpl _value, $Res Function(_$passwordUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$passwordUpdatedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$passwordUpdatedImpl implements passwordUpdated {
  const _$passwordUpdatedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ResetPasswordState.passwordUpdated(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$passwordUpdatedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$passwordUpdatedImplCopyWith<_$passwordUpdatedImpl> get copyWith =>
      __$$passwordUpdatedImplCopyWithImpl<_$passwordUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return passwordUpdated(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return passwordUpdated?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (passwordUpdated != null) {
      return passwordUpdated(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return passwordUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return passwordUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (passwordUpdated != null) {
      return passwordUpdated(this);
    }
    return orElse();
  }
}

abstract class passwordUpdated implements ResetPasswordState {
  const factory passwordUpdated(final String message) = _$passwordUpdatedImpl;

  String get message;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$passwordUpdatedImplCopyWith<_$passwordUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$resetPasswordErrorImplCopyWith<$Res> {
  factory _$$resetPasswordErrorImplCopyWith(_$resetPasswordErrorImpl value,
          $Res Function(_$resetPasswordErrorImpl) then) =
      __$$resetPasswordErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$resetPasswordErrorImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$resetPasswordErrorImpl>
    implements _$$resetPasswordErrorImplCopyWith<$Res> {
  __$$resetPasswordErrorImplCopyWithImpl(_$resetPasswordErrorImpl _value,
      $Res Function(_$resetPasswordErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$resetPasswordErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$resetPasswordErrorImpl implements resetPasswordError {
  const _$resetPasswordErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'ResetPasswordState.resetPasswordError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$resetPasswordErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$resetPasswordErrorImplCopyWith<_$resetPasswordErrorImpl> get copyWith =>
      __$$resetPasswordErrorImplCopyWithImpl<_$resetPasswordErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return resetPasswordError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return resetPasswordError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (resetPasswordError != null) {
      return resetPasswordError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return resetPasswordError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return resetPasswordError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (resetPasswordError != null) {
      return resetPasswordError(this);
    }
    return orElse();
  }
}

abstract class resetPasswordError implements ResetPasswordState {
  const factory resetPasswordError(final String? error) =
      _$resetPasswordErrorImpl;

  String? get error;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$resetPasswordErrorImplCopyWith<_$resetPasswordErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$otpVerificationErrorImplCopyWith<$Res> {
  factory _$$otpVerificationErrorImplCopyWith(_$otpVerificationErrorImpl value,
          $Res Function(_$otpVerificationErrorImpl) then) =
      __$$otpVerificationErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$otpVerificationErrorImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$otpVerificationErrorImpl>
    implements _$$otpVerificationErrorImplCopyWith<$Res> {
  __$$otpVerificationErrorImplCopyWithImpl(_$otpVerificationErrorImpl _value,
      $Res Function(_$otpVerificationErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$otpVerificationErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$otpVerificationErrorImpl implements otpVerificationError {
  const _$otpVerificationErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'ResetPasswordState.otpVerificationError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$otpVerificationErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$otpVerificationErrorImplCopyWith<_$otpVerificationErrorImpl>
      get copyWith =>
          __$$otpVerificationErrorImplCopyWithImpl<_$otpVerificationErrorImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return otpVerificationError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return otpVerificationError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (otpVerificationError != null) {
      return otpVerificationError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return otpVerificationError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return otpVerificationError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (otpVerificationError != null) {
      return otpVerificationError(this);
    }
    return orElse();
  }
}

abstract class otpVerificationError implements ResetPasswordState {
  const factory otpVerificationError(final String? error) =
      _$otpVerificationErrorImpl;

  String? get error;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$otpVerificationErrorImplCopyWith<_$otpVerificationErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$updatePasswordErrorImplCopyWith<$Res> {
  factory _$$updatePasswordErrorImplCopyWith(_$updatePasswordErrorImpl value,
          $Res Function(_$updatePasswordErrorImpl) then) =
      __$$updatePasswordErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$updatePasswordErrorImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$updatePasswordErrorImpl>
    implements _$$updatePasswordErrorImplCopyWith<$Res> {
  __$$updatePasswordErrorImplCopyWithImpl(_$updatePasswordErrorImpl _value,
      $Res Function(_$updatePasswordErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$updatePasswordErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$updatePasswordErrorImpl implements updatePasswordError {
  const _$updatePasswordErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'ResetPasswordState.updatePasswordError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$updatePasswordErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$updatePasswordErrorImplCopyWith<_$updatePasswordErrorImpl> get copyWith =>
      __$$updatePasswordErrorImplCopyWithImpl<_$updatePasswordErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) otpSent,
    required TResult Function(String error) wrongOtp,
    required TResult Function(String message) otpVerified,
    required TResult Function(String message) passwordUpdated,
    required TResult Function(String? error) resetPasswordError,
    required TResult Function(String? error) otpVerificationError,
    required TResult Function(String? error) updatePasswordError,
  }) {
    return updatePasswordError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? otpSent,
    TResult? Function(String error)? wrongOtp,
    TResult? Function(String message)? otpVerified,
    TResult? Function(String message)? passwordUpdated,
    TResult? Function(String? error)? resetPasswordError,
    TResult? Function(String? error)? otpVerificationError,
    TResult? Function(String? error)? updatePasswordError,
  }) {
    return updatePasswordError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? otpSent,
    TResult Function(String error)? wrongOtp,
    TResult Function(String message)? otpVerified,
    TResult Function(String message)? passwordUpdated,
    TResult Function(String? error)? resetPasswordError,
    TResult Function(String? error)? otpVerificationError,
    TResult Function(String? error)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (updatePasswordError != null) {
      return updatePasswordError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(otpSent value) otpSent,
    required TResult Function(wrongOtp value) wrongOtp,
    required TResult Function(otpVerified value) otpVerified,
    required TResult Function(passwordUpdated value) passwordUpdated,
    required TResult Function(resetPasswordError value) resetPasswordError,
    required TResult Function(otpVerificationError value) otpVerificationError,
    required TResult Function(updatePasswordError value) updatePasswordError,
  }) {
    return updatePasswordError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(otpSent value)? otpSent,
    TResult? Function(wrongOtp value)? wrongOtp,
    TResult? Function(otpVerified value)? otpVerified,
    TResult? Function(passwordUpdated value)? passwordUpdated,
    TResult? Function(resetPasswordError value)? resetPasswordError,
    TResult? Function(otpVerificationError value)? otpVerificationError,
    TResult? Function(updatePasswordError value)? updatePasswordError,
  }) {
    return updatePasswordError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(otpSent value)? otpSent,
    TResult Function(wrongOtp value)? wrongOtp,
    TResult Function(otpVerified value)? otpVerified,
    TResult Function(passwordUpdated value)? passwordUpdated,
    TResult Function(resetPasswordError value)? resetPasswordError,
    TResult Function(otpVerificationError value)? otpVerificationError,
    TResult Function(updatePasswordError value)? updatePasswordError,
    required TResult orElse(),
  }) {
    if (updatePasswordError != null) {
      return updatePasswordError(this);
    }
    return orElse();
  }
}

abstract class updatePasswordError implements ResetPasswordState {
  const factory updatePasswordError(final String? error) =
      _$updatePasswordErrorImpl;

  String? get error;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$updatePasswordErrorImplCopyWith<_$updatePasswordErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
