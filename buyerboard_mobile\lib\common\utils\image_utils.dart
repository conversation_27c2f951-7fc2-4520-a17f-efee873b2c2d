import 'dart:developer';
import 'dart:io';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/features/chat/data/models/upload_attachments_model.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_repository.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import '../../core/resources/resources.dart';
import '../../features/profile/domain/repository/user_repository.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker_android/image_picker_android.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';

class ImageUtils {
  final ImagePickerPlatform imagePickerImplementation =
      ImagePickerPlatform.instance;

  ImageUtils() {
    configureAndroidPhotoPicker();
  }

  void configureAndroidPhotoPicker() {
    if (imagePickerImplementation is ImagePickerAndroid) {
      (imagePickerImplementation as ImagePickerAndroid).useAndroidPhotoPicker =
          true;
    }
  }

  Future<String?> picImageFromGallery() async {
    String? path;
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      path = image.path;
    }
    return path;
  }

  Future<Map<String, dynamic>> pickMultipleImagesFromGallery() async {
    try {
      List<String> validImagePaths = [];
      bool hasInvalidFiles = false;
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultipleMedia(limit: 10);
      if (images.isNotEmpty) {
        for (var image in images) {
          File file = File(image.path);
          int fileSizeInBytes = await file.length();
          double fileSizeInMB = fileSizeInBytes / (1024 * 1024);
          if (fileSizeInMB <= 500) {
            validImagePaths.add(image.path);
          } else {
            hasInvalidFiles = true;
          }
        }
      }
      return {
        "valid": validImagePaths,
        "hasInvalidFiles": hasInvalidFiles,
      };
    } catch (e) {
      log(e.toString());
      return {
        "valid": [],
        "hasInvalidFiles": false,
        "error": "An error occurred: ${e.toString()}"
      };
    }
  }

  Future<List<String>?> pickMultipleDoc() async {
    try {
      List<String>? filePaths = [];
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
        ],
      );
      if (result != null && result.files.isNotEmpty) {
        filePaths = result.files.map((file) => file.path!).toList();
      }
      return filePaths;
    } catch (e) {
      return null;
    }
  }

  Future<String?> picAndCropImageFromGallery() async {
    final String? cropedImagePath;
    final String? imagePath = await picImageFromGallery();
    if (imagePath != null) {
      final CroppedFile? cropedImage = await _cropPickedImage(imagePath);
      cropedImage != null
          ? cropedImagePath = cropedImage.path
          : cropedImagePath = null;
    } else {
      cropedImagePath = null;
    }
    return cropedImagePath;
  }

  Future<CroppedFile?> _cropPickedImage(String path) async {
    CroppedFile? cropedImage = await ImageCropper().cropImage(
      sourcePath: path,
      compressQuality: 70,
      compressFormat: ImageCompressFormat.jpg,
      uiSettings: [
        AndroidUiSettings(
          aspectRatioPresets: [CropAspectRatioPreset.square],
          toolbarTitle: Strings.cropImage,
          toolbarColor: AppColors.primary,
          toolbarWidgetColor: Colors.white,
        ),
        IOSUiSettings(
          aspectRatioPresets: [CropAspectRatioPreset.square],
          title: Strings.cropImage,
        ),
      ],
    );
    return cropedImage;
  }

  Future<String?> uploadImage(String imagePath) async {
    final UserRepository userRepository = Injector.resolve();
    try {
      final imageFile = File(imagePath);
      final uploadImageResponse =
          await userRepository.uploadProfileImage(profileImage: imageFile);
      if (uploadImageResponse != null) {
        return (uploadImageResponse.imageUrl);
      }
    } catch (_) {
      return null;
    }
    return null;
  }

  Future<UploadAttachment?> uploadMultipleImages(String imagePaths) async {
    final ChatRepository chatRepository = Injector.resolve();
    try {
      File images = File(imagePaths);
      final uploadResponse =
          await chatRepository.uploadAttachments(images: images);
      if (uploadResponse != null) {
        return uploadResponse;
      } else {
        return null;
      }
    } catch (error) {
      log(error.toString());
      return null;
    }
  }
}
