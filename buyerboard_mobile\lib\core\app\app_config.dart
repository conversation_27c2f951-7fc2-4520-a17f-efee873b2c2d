enum Environment { dev, staging, prod }

extension XEnvironment on Environment {
  bool get isProd => this == Environment.prod;
  bool get isStaging => this == Environment.staging;
  bool get isDev => this == Environment.dev;
}

class AppConfig {
  static Environment environment = Environment.dev;
  late String baseUrl;
  late String socketBaseUrl;
  late String stripePublishableKey;
  late String oneSignalAppId;

  AppConfig.dev() {
    environment = Environment.dev;
    baseUrl = 'https://bb.vdev.tech/api';
    oneSignalAppId = '************************************';
    socketBaseUrl = 'wss://bb.vdev.tech';
  }

  AppConfig.staging() {
    environment = Environment.staging;
    baseUrl = 'https://bb.vdev.tech/api';
    oneSignalAppId = '************************************';
    socketBaseUrl = 'wss://bb.vdev.tech';
  }

  AppConfig.prod() {
    environment = Environment.prod;
    baseUrl = 'https://bba.vdev.tech/api';
    oneSignalAppId = '************************************';
    socketBaseUrl = 'wss://bba.vDev.tech';
  }
}
