import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:flutter/material.dart';

class LegalDocumentWrapper extends StatelessWidget {
  const LegalDocumentWrapper(
      {super.key,
      required this.title,
      required this.appBarSubTitle,
      required this.appBarTitle,
      required this.description});

  final String appBarTitle;
  final String appBarSubTitle;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ApplicationAppBar.buildAppBar(
        context,
        titleWidget: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              appBarTitle,
              style: context.textTheme.titleLarge
                  ?.copyWith(color: AppColors.white),
            ),

            Text(
              appBarSubTitle,
              style: context.textTheme.bodySmall
                  ?.copyWith(color: AppColors.primaryLight),
            ),
          ],
        ),
        leadingWidget: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              size: Dimensions.padding_28,
            ),
            color: AppColors.white,
            onPressed: () {
              context.shouldPop();
            }),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(Dimensions.materialPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: context.textTheme.titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: Dimensions.padding_8,),
            Text(description)
          ],
        ),
      ),
    );
  }
}
