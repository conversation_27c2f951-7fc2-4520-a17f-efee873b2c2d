import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';

sealed class AllArchiveChatsState {}

final class AllArchiveChatsInitialState extends AllArchiveChatsState {}

final class AllArchiveChatsLoadingState extends AllArchiveChatsState {}

final class AllArchiveChatsDataState extends AllArchiveChatsState {
  final List<ChatGroupModel> chats;
  AllArchiveChatsDataState(this.chats);
}

final class AllArchiveChatsErrorState extends AllArchiveChatsState {
  final String message;
  AllArchiveChatsErrorState(this.message);
}
