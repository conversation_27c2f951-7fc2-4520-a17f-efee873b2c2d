import 'package:buyer_board/common/widgets/profile_text_field.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/core/utils/text_field_formatters.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/resources/resources.dart';
import '../cubit/buyer_info_cubit.dart';

class BuyerDetailsSection extends StatelessWidget {
  const BuyerDetailsSection({
    super.key,
    required this.firstNameController,
    required this.lastNameController,
    required this.emailAddressController,
    required this.primaryPhoneNumberController,
    this.readOnly = true,
    required this.isEditMode,
  });
  final bool isEditMode;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final TextEditingController emailAddressController;
  final TextEditingController primaryPhoneNumberController;
  final bool readOnly;

  void onChangeFirstName(BuildContext context, String? firstName) =>
      context.read<BuyerInfoCubit>().setBuyerFirstName(firstName);
  void onChangeLastName(BuildContext context, String? lastName) =>
      context.read<BuyerInfoCubit>().setBuyerLastName(lastName);
  void onChangeEmailAddress(BuildContext context, String? email) =>
      context.read<BuyerInfoCubit>().setBuyerEmailAddress(email);
  void onChangePrimaryPhoneNumber(BuildContext context, String? number) =>
      context.read<BuyerInfoCubit>().setBuyerPrimaryPhoneNumber(number);
  void onChangeOptionalPhoneNumber(BuildContext context, String? number) =>
      context.read<BuyerInfoCubit>().setBuyerOptionalPhoneNumber(number);

  void clearEmail(BuildContext context) {
    context.read<BuyerInfoCubit>().setBuyerEmailAddress('');
  }

  void clearPhone(BuildContext context) {
    context.read<BuyerInfoCubit>().setBuyerPrimaryPhoneNumber('');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Flexible(
              child: ProfileTextField(
                controller: firstNameController,
                onChanged: (firstName) => onChangeFirstName(context, firstName),
                validator: Validators().requiredFieldValidator,
                label: Strings.firstName,
                hint: Strings.firstNameHint,
                readOnly: readOnly,
                textCapitalization: TextCapitalization.sentences,
              ),
            ),
            const SizedBox(width: 16),
            Flexible(
              child: ProfileTextField(
                controller: lastNameController,
                onChanged: (lastName) => onChangeLastName(context, lastName),
                validator: Validators().requiredFieldValidator,
                label: Strings.lastName,
                textCapitalization: TextCapitalization.sentences,
                hint: Strings.lastName,
                readOnly: readOnly,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: emailAddressController,
          keyboardType: TextInputType.emailAddress,
          onChanged: (email) => onChangeEmailAddress(context, email),
          validator: CoreUtils.emailValidator(isRequired: false),
          label: Strings.emailAddress,
          hint: Strings.emailAddressHint,
          readOnly: readOnly,
          onClear: () => clearEmail(context),
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: primaryPhoneNumberController,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            USPhoneNumberInputFormatter(),
          ],
          onChanged: (number) => onChangePrimaryPhoneNumber(context, number),
          validator: CoreUtils.phoneNumberValidator(isRequired: false),
          label: Strings.primaryPhoneNumber,
          hint: Strings.phoneNumberHint,
          readOnly: readOnly,
          onClear: () => clearPhone(context),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
