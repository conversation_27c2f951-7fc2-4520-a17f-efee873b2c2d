import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/features/menu/data/models/help_response.dart';
import 'package:buyer_board/features/menu/presentation/help/widgets/help_screen_topic_list_tile_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubit/help_screen_cubit.dart';
import '../state/help_screen_state.dart';

class TopicListWidget extends StatefulWidget {
  const TopicListWidget({super.key});

  @override
  State<TopicListWidget> createState() => _TopicListWidgetState();
}

class _TopicListWidgetState extends State<TopicListWidget> {
  @override
  void initState() {
    context.read<HelpScreenCubit>().getHelpTopics();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HelpScreenCubit, HelpScreenState>(
      builder: (context, state) {
        return state.maybeWhen(
          loading: () => Center(
            child: CupertinoActivityIndicator(
              radius: 16,
              color: context.colorScheme.primary,
            ),
          ),
          success: (help) => _buildTopicsList(help),
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildTopicsList(List<HelpResponse> help) {
    return Expanded(
      child: ListView.separated(
        separatorBuilder: (context, index) => Divider(
          color: context.colorScheme.outlineVariant,
        ),
        itemCount: help.length,
        itemBuilder: (context, index) => TopicListTileWidget(help: help[index]),
      ),
    );
  }
}
