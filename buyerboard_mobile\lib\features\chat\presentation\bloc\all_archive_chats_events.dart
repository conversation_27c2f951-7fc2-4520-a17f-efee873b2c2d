import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:equatable/equatable.dart';

sealed class AllArchiveChatsEvent extends Equatable {
  const AllArchiveChatsEvent();
}

class GetAllArchiveChats extends AllArchiveChatsEvent {
  @override
  List<Object?> get props => [];
}

// class AllArchiveChat extends AllArchiveChatsEvent {
//   final ChatPayload payload;
//   const AllArchiveChat(this.payload);

//   @override
//   List<Object> get props => [payload];
// }

class UnArchiveChat extends AllArchiveChatsEvent {
  final ChatPayload payload;
  const UnArchiveChat(this.payload);

  @override
  List<Object> get props => [payload];
}

// class DeleteChat extends AllArchiveChatsEvent {
//   final ChatPayload payload;
//   const DeleteChat(this.payload);

//   @override
//   List<Object> get props => [payload];
// }

class CloseArchiveChatConnection extends AllArchiveChatsEvent {
  @override
  List<Object?> get props => [];
}
