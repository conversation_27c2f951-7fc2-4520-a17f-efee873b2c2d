import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:flutter/material.dart';

class BBChip extends StatelessWidget {
  const BBChip({
    super.key,
    required this.label,
    this.icon,
    this.backgroundColor = AppColors.primary,
    this.labelColor = AppColors.white,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  });
  const BBChip.rental({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "RENTAL",
        icon = null,
        backgroundColor = AppColors.tertiary;
  const BBChip.asap({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "ASAP",
        icon = const Icon(Icons.today_rounded, color: Colors.white, size: 16),
        backgroundColor = AppColors.error;
  const BBChip.threeMonths({
    super.key,
    this.labelColor = Colors.black,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "3 MOs",
        icon = const Icon(Icons.today_rounded, color: Colors.black, size: 16),
        backgroundColor = AppColors.accent1;
  const BBChip.sixMonths({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "6 MOs",
        icon = const Icon(Icons.today_rounded, color: Colors.white, size: 16),
        backgroundColor = AppColors.success;
  const BBChip.open({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "Open",
        icon = const Icon(Icons.today_rounded, color: Colors.white, size: 16),
        backgroundColor = AppColors.accent2;
  const BBChip.notApproved({
    super.key,
    this.labelColor,
    this.outlined = true,
    this.borderRadius,
    this.padding,
  })  : label = "Not Approved",
        icon = const Icon(Icons.error, color: AppColors.greyLight, size: 16),
        backgroundColor = Colors.transparent;
  const BBChip.preApproved({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "Pre-Approved",
        icon = const Icon(Icons.thumb_up, color: Colors.white, size: 16),
        backgroundColor = AppColors.secondary;
  const BBChip.preQualified({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "Pre-Qualified",
        icon = const Icon(Icons.star, color: Colors.white, size: 16),
        backgroundColor = AppColors.tertiary;
  const BBChip.allCash({
    super.key,
    this.labelColor,
    this.outlined = false,
    this.borderRadius,
    this.padding,
  })  : label = "All Cash",
        icon = const Icon(Icons.monetization_on, color: Colors.white, size: 16),
        backgroundColor = AppColors.success;

  final String label;
  final Widget? icon;
  final Color backgroundColor;
  final Color? labelColor;
  final bool outlined;
  final BorderRadiusGeometry? borderRadius;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ??
          const EdgeInsets.symmetric(
            vertical: Dimensions.padding_4,
            horizontal: Dimensions.padding_12,
          ),
      decoration: BoxDecoration(
          border: outlined ? Border.all(color: AppColors.greyLight) : null,
          borderRadius: borderRadius ?? BorderRadius.circular(32),
          color: backgroundColor),
      child: Row(
        children: [
          Text(
            label,
            style: AppStyles.smallSemiBold.copyWith(
              color: labelColor ?? AppColors.white,
            ),
          ),
          if (icon != null) ...[const SizedBox(width: 8), icon!]
        ],
      ),
    );
  }
}
