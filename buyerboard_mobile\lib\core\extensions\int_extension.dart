extension IntExtension on num {
  String formatCurrency() {
    if (this < 1000) {
      return '\$$this';
    } else if (this >= 1000 && this < 1000000) {
      double result = this / 1000.0;
      return '\$${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}K';
    } else if (this >= 1000000 && this < 1000000000) {
      double result = this / 1000000.0;
      return '\$${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}M';
    } else {
      double result = this / 1000000000.0;
      return '\$${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}B';
    }
  }

  String formatNumber() {
    if (this < 1000) {
      return '$this';
    } else if (this >= 1000 && this < 1000000) {
      double result = this / 1000.0;
      return '${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}K';
    } else if (this >= 1000000 && this < 1000000000) {
      double result = this / 1000000.0;
      return '${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}M';
    } else {
      double result = this / 1000000000.0;
      return '${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}B';
    }
  }
}
