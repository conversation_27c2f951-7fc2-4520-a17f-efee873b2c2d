import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:flutter/material.dart';

class CommonTextButton extends StatelessWidget {
  const CommonTextButton(
      {super.key, required this.label, required this.onPressed});
  final String label;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: AppStyles.mediumSemiBold.copyWith(
            color: AppColors.primary,
          )),
      child: Text(label),
    );
  }
}
