import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/drawables.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class BuyerCardItem extends StatelessWidget {
  const BuyerCardItem({
    super.key,
    required this.buyer,
  });

  final BuyerModel buyer;

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final colors = context.appColors;
    final myBuyer = buyer.myBuyer;
    final backgroundColor = myBuyer ? colors.pPXLight : colors.gBlackGLight;
    final foregroundColor = myBuyer ? colors.whitePXDark : colors.whiteBlack;
    return GestureDetector(
      onTap: () => context.push(
        PagePath.expandedBuyerCard,
        extra: buyer,
      ),
      child: Ink(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(.25),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Hero(
              tag: 'property_image${buyer.id!}',
              child: _ImageBox(maxWidth: 128, imageUrl: buyer.propertyImage),
            ),
            spacerW8,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              buyer.sku ?? 'N/A',
                              style: typography.smallBlack
                                  .copyWith(color: foregroundColor),
                            ),
                            spacerH4,
                            Text(
                              myBuyer
                                  ? 'My buyer is looking to...'
                                  : 'This buyer is looking to...',
                              style: typography.mediumReg
                                  .copyWith(color: foregroundColor),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      _FavoriteIconButton(buyer: buyer, color: foregroundColor),
                    ],
                  ),
                  spacerH4,
                  Row(
                    children: [
                      Expanded(
                        child: _Chip(
                          label: buyer.buyerNeeds?.purchaseType?.label ?? 'N/A',
                          backgroundColor: foregroundColor,
                          foregroundColor: backgroundColor,
                        ),
                      ),
                      spacerW4,
                      Expanded(
                        child: _Chip(
                          label: buyer.buyerNeeds?.propertyType?.label ?? 'N/A',
                          backgroundColor: foregroundColor,
                          foregroundColor: backgroundColor,
                        ),
                      ),
                    ],
                  ),
                  spacerH4,
                  _Chip(
                    label:
                        'Up To: ${_formatLargeNumber(buyer.buyerNeeds?.budget, isCurrency: true)}${buyer.buyerNeeds?.purchaseType == PurchaseType.lease ? '/mo' : ''}',
                    backgroundColor: foregroundColor,
                    foregroundColor: backgroundColor,
                  ),
                  spacerH4,
                  _PropertySpecsTile(
                      buyer: buyer, foregroundColor: foregroundColor),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FavoriteIconButton extends StatefulWidget {
  const _FavoriteIconButton({
    super.key,
    required this.buyer,
    required this.color,
  });

  final BuyerModel buyer;
  final Color color;

  @override
  State<_FavoriteIconButton> createState() => _FavoriteIconButtonState();
}

class _FavoriteIconButtonState extends State<_FavoriteIconButton> {
  bool loading = false;
  Future<void> onFavouriteToggle() async {
    final isFav = widget.buyer.isFavourite;
     context.read<BuyersBloc>().add(
            UpdateBuyer(buyer: widget.buyer.copyWith(isFavourite: !isFav)));
    await context
        .read<FavouriteBuyersCubit>()
        .toggleFavourite(buyerId: widget.buyer.id!, isFavourite: !isFav)
        .then((val) {
      if (mounted) {
        context.read<BuyersBloc>().add(
            UpdateBuyer(buyer: widget.buyer.copyWith(isFavourite: !isFav)));
      }
      context.read<FavouriteBuyersCubit>().getFavouriteBuyers(silent: true);
    }).onError((err, _) {
      if (mounted) {
        setState(() {
          context.read<BuyersBloc>().add(
              UpdateBuyer(buyer: widget.buyer.copyWith(isFavourite: isFav)));
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onFavouriteToggle,
      child: Center(
        child: loading
            ? CupertinoActivityIndicator(
                color: widget.color,
              )
            : Icon(
                widget.buyer.isFavourite
                    ? Icons.favorite
                    : Icons.favorite_border,
                color: widget.color,
              ),
      ),
    );
  }
}

class _PropertySpecsTile extends StatelessWidget {
  const _PropertySpecsTile({
    required this.buyer,
    required this.foregroundColor,
  });

  final BuyerModel buyer;
  final Color foregroundColor;

  @override
  Widget build(BuildContext context) {
    final isLand = buyer.buyerNeeds?.propertyType == PropertyType.land;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (!isLand)
            _buildSpecsItem(
              Drawables.icBedroomOutlined,
              buyer.buyerNeeds?.minBedrooms.toString() ?? 'N/A',
              context,
              foregroundColor,
            ),
          if (!isLand)
            _buildSpecsItem(
              Drawables.icBathroomOutlined,
              buyer.buyerNeeds?.minBathrooms.toString() ?? 'N/A',
              context,
              foregroundColor,
            ),
          _buildSpecsItem(
            Drawables.icAreaOutlined,
            // buyer.buyerNeeds?.minArea ?? 'N/A',
            CoreUtils.formatLargeNumber(buyer.buyerNeeds?.minArea as num),
            context,
            foregroundColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSpecsItem(
      String icon, String label, BuildContext context, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          icon,
          width: 16,
          height: 16,
          colorFilter: ColorFilter.mode(
            color,
            BlendMode.srcIn,
          ),
        ),
        spacerW8,
        Text(
          label,
          style: context.typography.mediumReg.copyWith(color: color),
        ),
      ],
    );
  }
}

class _Chip extends StatelessWidget {
  const _Chip({
    required this.label,
    required this.backgroundColor,
    required this.foregroundColor,
  });

  final String label;
  final Color backgroundColor;
  final Color foregroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        textAlign: TextAlign.center,
        style: context.typography.mediumReg.copyWith(color: foregroundColor),
      ),
    );
  }
}

class _ImageBox extends StatelessWidget {
  const _ImageBox({
    required this.maxWidth,
    required this.imageUrl,
  });

  final double maxWidth;
  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Brightness.dark == context.theme.brightness;
    return Container(
      width: maxWidth,
      height: maxWidth,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: isDarkMode
            ? Border.all(
                color: context.appColors.black,
                width: 1,
              )
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.asset(
          imageUrl,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}

String _formatLargeNumber(String? value, {bool isCurrency = false}) {
  final formatted =
      CoreUtils.formatLargeNumber(double.tryParse(value ?? '0') ?? 0);
  return isCurrency ? '\$$formatted' : formatted;
}
