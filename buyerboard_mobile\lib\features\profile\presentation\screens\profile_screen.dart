import 'dart:async';
import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/profile/data/models/response/user_address_response.dart';
import 'package:buyer_board/features/profile/presentation/cubit/address_cubit.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:buyer_board/features/profile/presentation/states/address_state.dart';
import 'package:buyer_board/features/profile/presentation/states/profile_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/resources/resources.dart';
import '../widgets/brokerage_information_section.dart';
import '../widgets/personal_information_section.dart';
import '../widgets/profile_image_section.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailAddressController = TextEditingController();
  final primaryPhoneNumberController = TextEditingController();
  final secondaryPhoneNumberController = TextEditingController();
  final brokerageNameController = TextEditingController();
  final brokerageLicenseNumberController = TextEditingController();
  final brokerageStreetAddressController = TextEditingController();
  final brokerageCityController = TextEditingController();
  final brokerageStateController = TextEditingController();
  final zipCodeController = TextEditingController();
  final agentLicenseIdNoController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? addressError;
  bool edit = false;
  String? profileImage;
  BottomNavigationBarItem navBarItem(
          {required String label, required Widget icon}) =>
      BottomNavigationBarItem(icon: icon, label: label);
  void syncProfile(User user) {
    firstNameController.text = user.profile?.firstName ?? '';
    lastNameController.text = user.profile?.lastName ?? '';
    emailAddressController.text = user.profile?.agentEmail ?? '';
    primaryPhoneNumberController.text = user.profile?.primaryPhoneNumber ?? '';
    // secondaryPhoneNumberController.text = user.profile?.optionalPhoneNumber ?? '';
    brokerageNameController.text = user.profile?.brokerageName ?? '';
    brokerageLicenseNumberController.text =
        user.profile?.brokerageLisenceNo ?? '';
    brokerageStreetAddressController.text =
        user.profile?.brokerageStreetAddress ?? '';
    brokerageCityController.text = user.profile?.brokerageCity ?? '';
    brokerageStateController.text = user.profile?.brokerageState ?? '';
    zipCodeController.text = user.profile?.brokerageZipCode ?? '';
    agentLicenseIdNoController.text = user.profile?.agentLicenseIdNo ?? '';
    profileImage = user.profile?.imageUrl;
    setState(() {});
  }

  void onSave({String? imageUrl}) {
    context.read<ProfileCubit>().updateUserProfile(
          firstName: firstNameController.text.trim().isNotEmpty
              ? firstNameController.text.trim()
              : null,
          lastName: lastNameController.text.trim().isNotEmpty
              ? lastNameController.text.trim()
              : null,
          email: emailAddressController.text.trim().isNotEmpty
              ? emailAddressController.text.trim()
              : null,
          primaryPhoneNumber:
              primaryPhoneNumberController.text.trim().isNotEmpty
                  ? primaryPhoneNumberController.text.trim()
                  : null,
          optionalPhoneNumber:
              secondaryPhoneNumberController.text.trim().isNotEmpty
                  ? secondaryPhoneNumberController.text.trim()
                  : null,
          brokerageName: brokerageNameController.text.trim().isNotEmpty
              ? brokerageNameController.text.trim()
              : null,
          brokerageLisenceNo:
              brokerageLicenseNumberController.text.trim().isNotEmpty
                  ? brokerageLicenseNumberController.text.trim()
                  : null,
          brokerageStreetAddress:
              brokerageStreetAddressController.text.trim().isNotEmpty
                  ? brokerageStreetAddressController.text.trim()
                  : null,
          brokerageCity: brokerageCityController.text.trim().isNotEmpty
              ? brokerageCityController.text.trim()
              : null,
          brokerageZipCode: zipCodeController.text.trim().isNotEmpty
              ? zipCodeController.text.trim()
              : null,
          brokerageState: brokerageStateController.text.trim().isNotEmpty
              ? brokerageStateController.text.trim()
              : null,
          agentLicenseIdNo: agentLicenseIdNoController.text.trim().isNotEmpty
              ? agentLicenseIdNoController.text.trim()
              : null,
        );
  }

  void populateAddress(UserAddress address) {
    brokerageCityController.text = address.cityName;
    brokerageStateController.text = address.stateId;
    addressError = null;
    setState(() {});
  }

  void setAddressError(String error) {
    addressError = error;
    brokerageCityController.clear();
    brokerageStateController.clear();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    return BlocListener<ProfileCubit, ProfileState>(
      listener: (context, state) {
        state.mapOrNull(
          loading: (_) => Loader.show(),
          success: (state) {
            if (state.message != null) {
              context.showToast(isSuccess: true, message: state.message);
            }
            Loader.hide();
            syncProfile(state.user);
            setState(() {
              edit = false;
            });
          },
          profileError: (state) => {
            Loader.hide(),
            context.showToast(isSuccess: false, message: state.error)
          },
        );
      },
      child: BlocListener<AddressCubit, AddressState>(
        listener: (context, state) {
          state.mapOrNull(
            success: (state) {
              populateAddress(state.address);
            },
            addressError: (state) {
              setAddressError(state.error!);
            },
          );
        },
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            bottomNavigationBar: edit
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CommonButton.basic(
                      label: Strings.save,
                      backgroundColor: colorScheme.primary,
                      textColor: colorScheme.onPrimary,
                      action: () {
                        if (_formKey.currentState!.validate()
                         &&
                        addressError==null
                            ) {
                          onSave();
                        }
                      },
                    ),
                  )
                : null,
            appBar: AppBar(
              title: const Text(
                Strings.profile,
              ),
              leading: IconButton(
                icon: const Icon(Icons.menu),
                color: AppColors.white,
                onPressed: () {
                  context.push(PagePath.menu);
                },
              ),
              centerTitle: true,
              actions: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        edit = !edit;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        edit ? Strings.cancel : Strings.edit,
                        style: AppStyles.mediumSemiBold.copyWith(
                          color: colorScheme.onPrimaryFixed,
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(Dimensions.materialPadding),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    ProfileImageSection(profileImage: profileImage),
                    const SizedBox(height: 32),
                    PersonalInformationSection(
                      firstNameController: firstNameController,
                      lastNameController: lastNameController,
                      emailAddressController: emailAddressController,
                      primaryPhoneNumberController:
                          primaryPhoneNumberController,
                      secondaryPhoneNumberController:
                          secondaryPhoneNumberController,
                      agentLicenseIdNoController: agentLicenseIdNoController,
                      readOnly: !edit,
                    ),
                    spacerH24,
                    BrokerageInformationSection(
                      brokerageNameController: brokerageNameController,
                      brokerageLicenseNumberController:
                          brokerageLicenseNumberController,
                      brokerageStreetAddressController:
                          brokerageStreetAddressController,
                      brokerageCityController: brokerageCityController,
                      brokerageStateController: brokerageStateController,
                      zipCodeController: zipCodeController,
                      addressError: addressError,
                      readOnly: !edit,
                    ),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    emailAddressController.dispose();
    primaryPhoneNumberController.dispose();
    secondaryPhoneNumberController.dispose();
    brokerageNameController.dispose();
    brokerageLicenseNumberController.dispose();
    brokerageStreetAddressController.dispose();
    brokerageCityController.dispose();
    brokerageStateController.dispose();
    zipCodeController.dispose();
    agentLicenseIdNoController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    Future.microtask(
      () {
        final user = context.read<ProfileCubit>().getUserProfile();
        if (user != null && context.mounted) {
          setState(() {
            Future.microtask(() => syncProfile(user));
          });
        }
      },
    );
    super.initState();
  }

  // @override
  // FutureOr<void> onBuilt(BuildContext context) {
  //   Future.microtask(
  //     () => context.read<ProfileCubit>().getUserProfile(),
  //   );
  // }
}
