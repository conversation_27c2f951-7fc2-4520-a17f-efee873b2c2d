import 'package:buyer_board/common/widgets/common_text_form_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/address_from_zip_cubit.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/buyer_info_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BuyerLocationSection extends StatefulWidget {
  BuyerLocationSection(
      {Key? key, this.sync = false, required this.zipCodeControllers})
      : super(key: key);
  final bool sync;
  final List<ZipCodeEditingController> zipCodeControllers;

  @override
  State<BuyerLocationSection> createState() => _BuyerLocationSectionState();
}

class _BuyerLocationSectionState extends State<BuyerLocationSection> {
  // final List<ZipCodeEditingController> zipCodeControllers = [];

  @override
  void initState() {
    super.initState();
    widget.sync ? syncBuyerLocations() : onAddZipCode();
  }

  void onAddZipCode() {
    final controller = ZipCodeEditingController();
    context.read<BuyerInfoCubit>().addBuyerLocation(controller.text);
    widget.zipCodeControllers.add(controller);
    setState(() {});
  }

  void _appendCityName(ZipCodeEditingController controller) {
    final addressCubit = context.read<AddressFromZipCubit>();
    final zipCode = controller.zipCode;
    if (zipCode.length == 5) {
      addressCubit.getAddress(zipCode: zipCode).then((address) {
        if (address != null && context.mounted) {
          setState(() {
            controller.cityName = address.cityName;
            controller.errorText = null;
          });
        } else {
          setState(() {
            controller.cityName = null;
            controller.errorText = "Zip Code doesn't match any location in USA";
          });
        }
      });
    } else {
      setState(() {
        controller.cityName = null;
        controller.errorText = "Zip Code doesn't match any location in USA";
      });
    }
  }

  void syncBuyerLocations() {
    final buyerLocations =
        context.read<BuyerInfoCubit>().state.buyerLocationsOfInterest;
    for (var location in buyerLocations) {
      final controller = ZipCodeEditingController(text: location);
      widget.zipCodeControllers.add(controller);
      _appendCityName(controller);
    }
    setState(() {});
  }

  void onChangedZipCode(int index, String? value) {
    if (value == null || value.isEmpty) return;
    final duplicateIndex = widget.zipCodeControllers.indexWhere(
      (controller) =>
          controller.zipCode == value &&
          widget.zipCodeControllers.indexOf(controller) != index,
    );
    if (duplicateIndex != -1) {
      setState(() {
        widget.zipCodeControllers[index].errorText =
            "Duplicate ZIP Code. Please change.";
      });
    } else {
      setState(() {
        widget.zipCodeControllers[index].errorText = null;
      });
      context.read<BuyerInfoCubit>().updateBuyerLocation(index, value);
      _appendCityName(widget.zipCodeControllers[index]);
    }
  }

  void onRemove(int index) {
    widget.zipCodeControllers[index].dispose();
    context.read<BuyerInfoCubit>().removeBuyerLocation(index);
    widget.zipCodeControllers.removeAt(index);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final colors = context.colorScheme;
    final xColors = context.appColors;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.buyerLocation,
          style: typography.large1xBlack,
        ),
        Text(
          Strings.buyerLocationDesc,
          style: typography.mediumReg,
        ),
        spacerH16,
        ...List.generate(
          widget.zipCodeControllers.length,
          (index) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: CommonTextFormField(
                    controller: widget.zipCodeControllers[index],
                    onChanged: (value) {
                      if (value != null && value.length > 4) {
                        onChangedZipCode(index, value);
                      } else if (value!.isEmpty) {
                        setState(() {
                          widget.zipCodeControllers[index].cityName = null;
                          widget.zipCodeControllers[index].errorText = null;
                        });
                      }
                    },
                    validator: CoreUtils.zipCodeFormatter(),
                    keyboardType: const TextInputType.numberWithOptions(
                      signed: false,
                      decimal: false,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(5),
                    ],
                    fillColor: Colors.transparent,
                    label: Strings.zipCode,
                    hint: Strings.zipCodeHint,
                    errorText: widget.zipCodeControllers[index].errorText,
                    labelStyle:
                        typography.smallReg.copyWith(color: xColors.greyM),
                    hintTextStyle: typography.largeReg
                        .copyWith(color: xColors.greyLightGreyDefault),
                    contextTextStyle: typography.largeReg,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    borderColor: colors.outline,
                    suffixIcon: index == 0
                        ? null
                        : InkWell(
                            onTap: () {
                              onRemove(index);
                            },
                            child: const Icon(Icons.close),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
        TextButton.icon(
          onPressed: onAddZipCode,
          label: const Text('Add a Zip Code'),
          icon: const Icon(Icons.add),
        ),
      ],
    );
  }
}

class ZipCodeEditingController extends TextEditingController {
  String? cityName;
  String? errorText;

  ZipCodeEditingController({String? text, this.cityName, this.errorText})
      : super(text: text);

  String get zipCode => text;

  @override
  TextSpan buildTextSpan(
      {required BuildContext context,
      TextStyle? style,
      required bool withComposing}) {
    final List<TextSpan> children = [
      TextSpan(text: text, style: style),
    ];

    if (cityName != null && cityName!.isNotEmpty) {
      children.add(TextSpan(
        text: ' $cityName',
        style: style?.copyWith(color: context.appColors.greyDefaultGreyMedium),
      ));
    }
    return TextSpan(style: style, children: children);
  }
}
