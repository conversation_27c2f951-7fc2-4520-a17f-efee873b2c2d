import 'dart:developer';

import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/domain/repositories/all_chats_repository.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chat_states.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_events.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AllChatsBloc extends Bloc<AllChatsEvent, AllChatsState> {
  final AllChatsRepository chatRepository;
  final List<ChatGroupModel> _messages = [];

  AllChatsBloc({required this.chatRepository}) : super(AllChatsInitialState()) {
    on<GetAllChats>(_onLoadAllChat);
    on<ArchiveChat>(_onArchiveChat);
    on<CloseAllChatConnections>((event, emit) {
      chatRepository.closeConnection();
    });
    on<SeenChatEvent>(_onSeenChat);
  }

  void _onLoadAllChat(GetAllChats event, Emitter<AllChatsState> emit) async {
    chatRepository.initializeConnection();
    _messages.clear();
    emit(AllChatsLoadingState());
    try {
      final response = chatRepository.getAllChats();
      await for (final record in response) {
        _messages.clear();
        _messages.addAll(record.data);
        emit(AllChatsDataState(_messages));
      }
    } catch (e) {
      log("CHATT ERRORR $e");
      emit(AllChatsErrorState(e.toString()));
    }
  }

  void _onArchiveChat(ArchiveChat event, Emitter<AllChatsState> emit) async {
    chatRepository.archiveChat(event.payload);
  }

  void _onSeenChat(SeenChatEvent event, Emitter<AllChatsState> emit) async {
    final currentBuyerIndex =
        _messages.indexWhere((element) => element.buyerId == event.buyerId);
    if (currentBuyerIndex != -1) {
      final currentThreadIndex = _messages[currentBuyerIndex]
          .threads
          .indexWhere((element) => element.id == event.threadId);
      if (currentThreadIndex != -1) {
        final updatedThread = _messages[currentBuyerIndex]
            .threads[currentThreadIndex]
            .copyWith(hasNewMessage: false);

        // Create a new list of threads with the updated thread
        final updatedThreads = List<ChatGroupThreadModel>.from(
            _messages[currentBuyerIndex].threads)
          ..[currentThreadIndex] = updatedThread;

        // Create a new ChatGroupModel with the updated threads
        final updatedChatGroup = _messages[currentBuyerIndex].copyWith(
          threads: updatedThreads,
        );

        // Replace the old ChatGroupModel with the new one
        _messages[currentBuyerIndex] = updatedChatGroup;

        // Emit the updated state
        emit(AllChatsDataState(_messages));
      }
    }
  }
}
