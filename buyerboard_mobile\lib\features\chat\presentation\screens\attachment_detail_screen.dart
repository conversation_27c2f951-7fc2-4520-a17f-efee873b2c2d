import 'dart:developer';
import 'dart:io';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player/video_player.dart';

// ignore: must_be_immutable
class SendAttachmentScreen extends StatefulWidget {
  final List<String> imagePaths;
  final int userId;
  final int otherUserId;
  final int buyerId;
  final Stream<ChatMessage?> stream;
  final Stream<ChatMessage?> editStream;
  final VoidCallback onCancelReply;
  int? parentMessageId;
  int? editMessageId;
  bool isMessageEmpty;

  SendAttachmentScreen({
    super.key,
    required this.imagePaths,
    required this.userId,
    required this.otherUserId,
    required this.buyerId,
    required this.stream,
    required this.editStream,
    required this.onCancelReply,
    this.parentMessageId,
    this.editMessageId,
    this.isMessageEmpty = true,
  });

  @override
  _SendAttachmentScreenState createState() => _SendAttachmentScreenState();
}

class _SendAttachmentScreenState extends State<SendAttachmentScreen> {
  String? selectedFilePath;
  VideoPlayerController? _videoPlayerController;
  TextEditingController messageController = TextEditingController();
  bool isVideoPlaying = false;
  bool _isMessageEmpty = true;

  @override
  void initState() {
    super.initState();
    selectedFilePath =
        widget.imagePaths.isNotEmpty ? widget.imagePaths[0] : null;
    if (selectedFilePath != null && isVideoFile(selectedFilePath!)) {
      _initializeVideoController(selectedFilePath!);
    }
    messageController.addListener(() {
      setState(() {
        _isMessageEmpty = messageController.text.trim().isEmpty;
      });
    });
    _processAndSendAttachments();
  }

  List<Map<String, dynamic>> attachments = [];
  void _processAndSendAttachments() async {
    if (widget.imagePaths.isEmpty) return;
    Loader.show(title: "loading...");
    for (String imagePath in widget.imagePaths) {
      try {
        final uploadResponse =
            await context.read<ChatBloc>().uploadAttachments(imagePath);
        if (uploadResponse != null) {
          attachments.add({
            'type': uploadResponse.type,
            'url': uploadResponse.url,
            if (uploadResponse.thumbnailUrl != null)
              'thumbnail_url': uploadResponse.thumbnailUrl,
          });
        }
      } catch (e) {
        log("Error uploading attachment: $e");
      }
    }
    setState(() {});
    Loader.hide();
  }

  void _deleteAttachments() async {
    if (attachments.isEmpty) return;
    try {
      final uploadResponse =
          await context.read<ChatBloc>().deleteAttachments(attachments);
      if (uploadResponse != null) {
        Navigator.pop(context);
      }
    } catch (e) {
      log("Error deleting  attachment: $e");
    }
  }

  void _onThumbnailTap(String imagePath) {
    setState(() {
      selectedFilePath = imagePath;
      if (isVideoFile(imagePath)) {
        _videoPlayerController?.dispose();
        _initializeVideoController(imagePath);
      } else {
        _videoPlayerController?.dispose();
        _videoPlayerController = null;
        isVideoPlaying = false;
      }
    });
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    super.dispose();
  }

  bool isVideoFile(String path) {
    final videoExtensions = ['mp4', 'mov', 'avi', 'mkv'];
    final fileExtension = path.split('.').last.toLowerCase();
    return videoExtensions.contains(fileExtension);
  }

  void _initializeVideoController(String videoPath) {
    _videoPlayerController = VideoPlayerController.file(File(videoPath))
      ..initialize().then((_) {
        setState(() {
          isVideoPlaying = _videoPlayerController!.value.isPlaying;
        });
      });
  }

  void _toggleVideoPlayback() {
    if (_videoPlayerController == null) return;

    if (_videoPlayerController!.value.isPlaying) {
      _videoPlayerController!.pause();
      setState(() {
        isVideoPlaying = false;
      });
    } else {
      _videoPlayerController!.play();
      setState(() {
        isVideoPlaying = true;
      });
    }
  }

  Widget _buildThumbnail(String filePath) {
    bool isVideo = isVideoFile(filePath);
    return GestureDetector(
      onTap: () => _onThumbnailTap(filePath),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                selectedFilePath == filePath ? Colors.blue : Colors.transparent,
            width: 2,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            isVideo
                ? Icon(
                    Icons.play_circle_outline,
                    color: context.theme.appColors.blackWhite,
                    size: 24,
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(filePath),
                      fit: BoxFit.cover,
                      width: 50,
                      height: 50,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: const Text("Image Details"),
          leading: IconButton(
              onPressed: () {
                _deleteAttachments();
              },
              icon: const Icon(
                Icons.clear,
                color: Colors.white,
              )),
        ),
        bottomNavigationBar: attachments.isNotEmpty
            ? Padding(
                padding: EdgeInsets.only(
                  left: 16,
                  right: 16,
                  top: 16,
                  bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                ),
                child: SizedBox(
                  height: 39,
                  child: TextField(
                    controller: messageController,
                    onSubmitted: (value) => _sendMessage(),
                    textInputAction: TextInputAction.send,
                    textCapitalization: TextCapitalization.sentences,
                    decoration: InputDecoration(
                      counterText: '',
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 0,
                      ),
                      isDense: true,
                      hintText: 'iMessage',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(100),
                        borderSide: BorderSide(
                          color: colors.outline,
                        ),
                      ),
                      suffixIcon: IconButton(
                        onPressed: _sendMessage,
                        icon: Icon(
                          Icons.send,
                          size: 20,
                          color: _isMessageEmpty
                              ? colors.onSurface
                              : colors.primary,
                        ),
                      ),
                    ),
                  ),
                ),
              )
            : const SizedBox(),
        body: Stack(children: [
          Center(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: selectedFilePath != null
                  ? isVideoFile(selectedFilePath!)
                      ? _videoPlayerController != null &&
                              _videoPlayerController!.value.isInitialized
                          ? Stack(
                              alignment: Alignment.center,
                              children: [
                                AspectRatio(
                                  aspectRatio:
                                      _videoPlayerController!.value.aspectRatio,
                                  child: VideoPlayer(_videoPlayerController!),
                                ),
                                GestureDetector(
                                  onTap: _toggleVideoPlayback,
                                  child: CircleAvatar(
                                    radius: 30,
                                    backgroundColor: Colors.black54,
                                    child: Icon(
                                      isVideoPlaying
                                          ? Icons.pause
                                          : Icons.play_arrow,
                                      color: Colors.white,
                                      size: 36,
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : const CircularProgressIndicator()
                      : Image.file(
                          File(selectedFilePath!),
                          fit: BoxFit.cover,
                        )
                  : const Text('No file selected'),
            ),
          ),
          if (widget.imagePaths.length > 1)
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 70,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      for (int i = 0; i < widget.imagePaths.length; i++)
                        _buildThumbnail(widget.imagePaths[i]),
                    ],
                  ),
                ),
              ),
            ),
        ]));
  }

  void _sendMessage() async {
    bool hasTextMessage = messageController.text.trim().isNotEmpty;
    String textMessage = messageController.text.trim();
    Loader.show();
    if (attachments.isNotEmpty) {
      int selectedIndex = widget.imagePaths.indexOf(selectedFilePath!);
      for (int i = 0; i < attachments.length; i++) {
        String message =
            (i == selectedIndex && hasTextMessage) ? textMessage : "";
        final offset = DateTime.now().timeZoneOffset.inHours;
        final payload = SendMessagePayload(
          buyerId: widget.buyerId,
          message: message,
          userId: widget.userId,
          otherUserId: widget.otherUserId,
          offset: offset,
          attachments: [attachments[i]],
        );
        
        context.read<ChatBloc>().add(SendMessage(payload));
      }
    } else if (widget.parentMessageId != null &&
        widget.parentMessageId != 0 &&
        hasTextMessage) {
           log("come in reply ");
      final payload = SendReplyMessagePayload(
        buyerId: widget.buyerId,
        message: textMessage,
        userId: widget.userId,
        otherUserId: widget.otherUserId,
        parentId: widget.parentMessageId!,
      );
      context.read<ChatBloc>().add(SendMessage(payload));
      widget.parentMessageId = null;
      widget.onCancelReply();
    } else if (widget.editMessageId != null &&
        widget.editMessageId != 0 &&
        hasTextMessage) {
          log("come in edit ");
      final payload = EditMessagePayload(
        userId: widget.userId,
        otherUserId: widget.otherUserId,
        buyerId: widget.buyerId,
        messageId: widget.editMessageId!,
        message: textMessage,
      );
      context.read<ChatBloc>().add(SendMessage(payload));
      widget.editMessageId = null;
      widget.onCancelReply();
    }

    Loader.hide();
    messageController.clear();
    Navigator.pop(context);
  }



}
