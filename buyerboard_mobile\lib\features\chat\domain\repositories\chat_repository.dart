import 'dart:io';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/data/models/upload_attachments_model.dart';
import 'package:buyer_board/features/chat/domain/entities/chat_event_enum.dart';

abstract interface class ChatRepository {
  // Method to establish the WebSocket connection
  void initializeConnection();

  // Method to close the WebSocket connection
  void closeConnection();


Future<void> get connectionReady;
  // Method to get all conversations
  // Stream<({ChatEventType event, List<ChatGroupModel> data})> getAllChats();

  // Method to get all archive chats
  // Stream<({ChatEventType event, List<ChatGroupModel> data})> getAllArchiveChats();

  // Method to listen to a specific conversation
  Stream<({ChatEventType event, List<ChatMessage> data})> getChat(
    ChatPayload payload,
  );

  // Method to send a message in a specific conversation
  Future<void> sendMessage(ChatPayload payload);

  // Method to delete a message by its ID
  Future<void> deleteMessages(ChatPayload payload);

  Future<UploadAttachment?> uploadAttachments({required File images});

  Future<String> deleteAttachments({required List<Map<String, dynamic>> images});


  // Method to archive a chat between two people
  // Future<void> archiveChat(ChatPayload payload);

  // Method to unarchive a chat between two people
  // Future<void> unarchiveChat(ChatPayload payload);

  // Stream<ChatMessage> subscribeNotifications();
}
