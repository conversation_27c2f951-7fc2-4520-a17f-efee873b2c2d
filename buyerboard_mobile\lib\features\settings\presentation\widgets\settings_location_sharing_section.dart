import 'package:buyer_board/common/widgets/app_switch_tile.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/features/settings/presentation/cubit/share_location_cubit.dart';
import 'package:buyer_board/features/settings/presentation/widgets/setting_section_base_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SettingsLocationSharingSection extends StatefulWidget {
  const SettingsLocationSharingSection({super.key});

  @override
  State<SettingsLocationSharingSection> createState() =>
      _SettingsLocationSharingSectionState();
}

class _SettingsLocationSharingSectionState
    extends State<SettingsLocationSharingSection> {
  @override
  Widget build(BuildContext context) {
    return SettingSectionBaseWidget(
      title: Strings.locationSharing,
      subtitle: Strings.locationSharingSubtitle,
      child:
          <PERSON><PERSON><PERSON>er<ShareLocationCubit, bool>(builder: (_, locationEnabled) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppSwitch(
              title: Strings.shareLocation,
              isActive: locationEnabled,
              onChanged: (value) {
                setState(() {
                  context.read<ShareLocationCubit>().update(value);
                });
              },
            ),
            if (!locationEnabled)
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${Strings.note}: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: context.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    TextSpan(
                      text: Strings.shareLocationSubtitle,
                      style: TextStyle(
                        color: context.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      }),
    );
  }
}
