import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_state.freezed.dart';

@freezed
class ProfileState with _$ProfileState {
  const factory ProfileState.initial() = initial;
  const factory ProfileState.loading() = loading;
  const factory ProfileState.success({required User user, String? message}) =
      success;
  const factory ProfileState.profileError(String? error) = loginError;
  const factory ProfileState.imageUploaded({required String imageUrl, String? message}) = imageUploaded;
}
