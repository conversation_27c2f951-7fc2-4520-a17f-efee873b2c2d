import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'buyer_model.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
)
class BuyerModelResponse {
  BuyerModelResponse({
    required this.data,
    required this.pagination,
  });

  final List<BuyerModel> data;
  final Pagination pagination;

  factory BuyerModelResponse.fromJson(Map<String, dynamic> json) =>
      _$BuyerModelResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BuyerModelResponseToJson(this);
}

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
)
class Pagination {
  Pagination({
    required this.totalPages,
    required this.totalEntries,
    required this.pageSize,
    required this.currentPage,
  });

  final int totalPages;
  final int totalEntries;
  final int pageSize;
  final int currentPage;

  factory Pagination.fromJson(Map<String, dynamic> json) =>
      _$PaginationFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationToJson(this);
}

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
  includeIfNull: false,
)
class BuyerModel {
  BuyerModel({
    this.user,
    this.imageUrl,
    this.timeOffset,
    this.id,
    this.firstName,
    this.lastName,
    this.buyersAlias,
    this.email,
    this.primaryPhoneNumber,
    this.optionalPhoneNumber,
    this.buyerLocationsOfInterest = const [],
    this.buyerNeeds,
    this.isFavourite = false,
    this.notes,
    this.additionalRequests = const [],
    this.buyerExpirationDate,
    this.insertedAt,
    this.updatedAt,
    this.myBuyer = false,
    this.sku,
  });
  int? id;
  User? user;
  String? imageUrl;
  int? timeOffset;
  String? firstName;
  String? lastName;
  String? buyersAlias;
  String? email;
  String? primaryPhoneNumber;
  String? optionalPhoneNumber;
  List<String> buyerLocationsOfInterest;
  @JsonKey(name: "buyer_need")
  BuyerNeeds? buyerNeeds;
  bool isFavourite;
  @JsonKey(name: "note")
  String? notes;
  List<String> additionalRequests;
  DateTime? buyerExpirationDate;
  DateTime? insertedAt;
  DateTime? updatedAt;
  String? sku;
  bool myBuyer;

  factory BuyerModel.fromJson(Map<String, dynamic> json) =>
      _$BuyerModelFromJson(json);

  BuyerModel copyWith({bool? isFavourite}) {
    return BuyerModel(
      id: id,
      imageUrl: imageUrl,
      timeOffset: timeOffset,
      user: user,
      firstName: firstName,
      lastName: lastName,
      buyersAlias: buyersAlias,
      email: email,
      primaryPhoneNumber: primaryPhoneNumber,
      optionalPhoneNumber: optionalPhoneNumber,
      buyerLocationsOfInterest: buyerLocationsOfInterest,
      buyerNeeds: buyerNeeds,
      isFavourite: isFavourite ?? this.isFavourite,
      notes: notes,
      additionalRequests: additionalRequests,
      buyerExpirationDate: buyerExpirationDate,
      insertedAt: insertedAt,
      updatedAt: updatedAt,
      myBuyer: myBuyer,
      sku: sku,
    );
  }

  Map<String, dynamic> toJson() => _$BuyerModelToJson(this);

  String get propertyImage {
// "property_type": " single_family_house, townhouse, condo, apartment, multi_family_house, mobile, one of them",
    return switch (buyerNeeds?.propertyType) {
      PropertyType.singleFamily =>
        myBuyer ? Drawables.singleFamily : Drawables.singleFamilyMonochrome,
      PropertyType.townhouse =>
        myBuyer ? Drawables.townhouse : Drawables.townhouseMonochrome,
      PropertyType.condo =>
        myBuyer ? Drawables.condo : Drawables.condoMonochrome,
      PropertyType.apartment =>
        myBuyer ? Drawables.apartment : Drawables.apartmentMonochrome,
      PropertyType.multiFamily =>
        myBuyer ? Drawables.multiFamily : Drawables.multiFamilyMonochrome,
      PropertyType.land => myBuyer ? Drawables.land : Drawables.landMonochrome,
      _ => Drawables.apartment,
    };
  }
}

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
)
class BuyerNeeds {
  BuyerNeeds({
    this.purchaseType,
    this.propertyType,
    this.financialStatus,
    this.budget,
    this.minBedrooms,
    this.minBathrooms,
    this.minArea,
  });
  @JsonKey(name: "purchase_type")
  PurchaseType? purchaseType;
  PropertyType? propertyType;
  FinancialStatus? financialStatus;
  @JsonKey(name: "budget_upto")
  String? budget;
  double? minBedrooms;
  double? minBathrooms;
  double? minArea;
  factory BuyerNeeds.fromJson(Map<String, dynamic> json) =>
      _$BuyerNeedsFromJson(json);

  Map<String, dynamic> toJson() => _$BuyerNeedsToJson(this);
}

enum PurchaseType {
  @JsonValue('purchase')
  purchase,
  @JsonValue('lease')
  lease;

  String get label {
    return switch (this) {
      PurchaseType.purchase => 'Purchase',
      PurchaseType.lease => 'Lease',
    };
  }

  String get jsonValue {
    return switch (this) {
      PurchaseType.purchase => 'purchase',
      PurchaseType.lease => 'lease',
    };
  }
}

enum PropertyType {
  @JsonValue('single_family_house')
  singleFamily,
  @JsonValue('townhouse')
  townhouse,
  @JsonValue('condo')
  condo,
  @JsonValue('apartment')
  apartment,
  @JsonValue('multi_family_house')
  multiFamily,
  @JsonValue('land')
  land;

  String get label {
    return switch (this) {
      PropertyType.singleFamily => 'Single Family',
      PropertyType.townhouse => 'Townhouse',
      PropertyType.condo => 'Condo',
      PropertyType.apartment => 'Apartment',
      PropertyType.multiFamily => 'Multi Family',
      PropertyType.land => 'Land',
    };
  }

  String get jsonValue {
    return switch (this) {
      PropertyType.singleFamily => 'single_family_house',
      PropertyType.townhouse => 'townhouse',
      PropertyType.condo => 'condo',
      PropertyType.apartment => 'apartment',
      PropertyType.multiFamily => 'multi_family_house',
      PropertyType.land => 'land',
    };
  }
}

enum FinancialStatus {
  @JsonValue('pre_qualified')
  preQualified,
  @JsonValue('pre_approved')
  preApproved,
  @JsonValue('all_cash')
  allCash,
  @JsonValue('n_a')
  nA;

  String get label {
    return switch (this) {
      FinancialStatus.preQualified => 'Pre-Qualified',
      FinancialStatus.preApproved => 'Pre-Approved',
      FinancialStatus.allCash => 'All Cash',
      FinancialStatus.nA => 'N/A',
    };
  }

  String get jsonValue {
    return switch (this) {
      FinancialStatus.preQualified => 'pre_qualified',
      FinancialStatus.preApproved => 'pre_approved',
      FinancialStatus.allCash => 'all_cash',
      FinancialStatus.nA => 'n_a',
    };
  }
}
