import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/splash/presentation/state/splash_screen_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SplashCubit extends Cubit<SplashScreenState> {
  SplashCubit({
    required this.appPreferences,
  }) : super(const SplashScreenState.initial());
  final AppPreferences appPreferences;

  Future<void> initSplash() async {
    Future.delayed(const Duration(seconds: 3), () async {
      //For Local Auth
      final authResponse = await appPreferences.getUserCredentials();
      if (authResponse != null) {
        // authActionNotifier.value = AuthAction.none;
        emit(const SplashScreenState.localAuth());
        return;
      }

      final user = appPreferences.getUser();
      // final googleAuthToken = appPreferences.getGoogleAuthToken();
      // final appleAuthToken = appPreferences.getAppleAuthToken();
      if (user != null) {
        print("User Found ${user.toJson()}");
        final isFreshInstall = appPreferences.isFreshInstall();
        if (isFreshInstall) {
          emit(const SplashScreenState.success(PagePath.mainScreen));
        } else {
          emit(const SplashScreenState.success(PagePath.mainScreen));
        }
      } else {
        print("No User Found");
        emit(const SplashScreenState.success(PagePath.authScreen));
      }
    });
  }
}
