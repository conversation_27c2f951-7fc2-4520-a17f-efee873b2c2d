// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'social_auth_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SocialAuthState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(dynamic data) socialAuthSuccess,
    required TResult Function(String? error) socialAuthError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(dynamic data)? socialAuthSuccess,
    TResult? Function(String? error)? socialAuthError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(dynamic data)? socialAuthSuccess,
    TResult Function(String? error)? socialAuthError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(googleAuthSuccess value) socialAuthSuccess,
    required TResult Function(socialAuthError value) socialAuthError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult? Function(socialAuthError value)? socialAuthError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult Function(socialAuthError value)? socialAuthError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialAuthStateCopyWith<$Res> {
  factory $SocialAuthStateCopyWith(
          SocialAuthState value, $Res Function(SocialAuthState) then) =
      _$SocialAuthStateCopyWithImpl<$Res, SocialAuthState>;
}

/// @nodoc
class _$SocialAuthStateCopyWithImpl<$Res, $Val extends SocialAuthState>
    implements $SocialAuthStateCopyWith<$Res> {
  _$SocialAuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$SocialAuthStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'SocialAuthState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(dynamic data) socialAuthSuccess,
    required TResult Function(String? error) socialAuthError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(dynamic data)? socialAuthSuccess,
    TResult? Function(String? error)? socialAuthError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(dynamic data)? socialAuthSuccess,
    TResult Function(String? error)? socialAuthError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(googleAuthSuccess value) socialAuthSuccess,
    required TResult Function(socialAuthError value) socialAuthError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult? Function(socialAuthError value)? socialAuthError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult Function(socialAuthError value)? socialAuthError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements SocialAuthState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$SocialAuthStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'SocialAuthState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(dynamic data) socialAuthSuccess,
    required TResult Function(String? error) socialAuthError,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(dynamic data)? socialAuthSuccess,
    TResult? Function(String? error)? socialAuthError,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(dynamic data)? socialAuthSuccess,
    TResult Function(String? error)? socialAuthError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(googleAuthSuccess value) socialAuthSuccess,
    required TResult Function(socialAuthError value) socialAuthError,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult? Function(socialAuthError value)? socialAuthError,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult Function(socialAuthError value)? socialAuthError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements SocialAuthState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$googleAuthSuccessImplCopyWith<$Res> {
  factory _$$googleAuthSuccessImplCopyWith(_$googleAuthSuccessImpl value,
          $Res Function(_$googleAuthSuccessImpl) then) =
      __$$googleAuthSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$$googleAuthSuccessImplCopyWithImpl<$Res>
    extends _$SocialAuthStateCopyWithImpl<$Res, _$googleAuthSuccessImpl>
    implements _$$googleAuthSuccessImplCopyWith<$Res> {
  __$$googleAuthSuccessImplCopyWithImpl(_$googleAuthSuccessImpl _value,
      $Res Function(_$googleAuthSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_$googleAuthSuccessImpl(
      freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _$googleAuthSuccessImpl implements googleAuthSuccess {
  const _$googleAuthSuccessImpl(this.data);

  @override
  final dynamic data;

  @override
  String toString() {
    return 'SocialAuthState.socialAuthSuccess(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$googleAuthSuccessImpl &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$googleAuthSuccessImplCopyWith<_$googleAuthSuccessImpl> get copyWith =>
      __$$googleAuthSuccessImplCopyWithImpl<_$googleAuthSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(dynamic data) socialAuthSuccess,
    required TResult Function(String? error) socialAuthError,
  }) {
    return socialAuthSuccess(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(dynamic data)? socialAuthSuccess,
    TResult? Function(String? error)? socialAuthError,
  }) {
    return socialAuthSuccess?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(dynamic data)? socialAuthSuccess,
    TResult Function(String? error)? socialAuthError,
    required TResult orElse(),
  }) {
    if (socialAuthSuccess != null) {
      return socialAuthSuccess(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(googleAuthSuccess value) socialAuthSuccess,
    required TResult Function(socialAuthError value) socialAuthError,
  }) {
    return socialAuthSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult? Function(socialAuthError value)? socialAuthError,
  }) {
    return socialAuthSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult Function(socialAuthError value)? socialAuthError,
    required TResult orElse(),
  }) {
    if (socialAuthSuccess != null) {
      return socialAuthSuccess(this);
    }
    return orElse();
  }
}

abstract class googleAuthSuccess implements SocialAuthState {
  const factory googleAuthSuccess(final dynamic data) = _$googleAuthSuccessImpl;

  dynamic get data;

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$googleAuthSuccessImplCopyWith<_$googleAuthSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$socialAuthErrorImplCopyWith<$Res> {
  factory _$$socialAuthErrorImplCopyWith(_$socialAuthErrorImpl value,
          $Res Function(_$socialAuthErrorImpl) then) =
      __$$socialAuthErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$socialAuthErrorImplCopyWithImpl<$Res>
    extends _$SocialAuthStateCopyWithImpl<$Res, _$socialAuthErrorImpl>
    implements _$$socialAuthErrorImplCopyWith<$Res> {
  __$$socialAuthErrorImplCopyWithImpl(
      _$socialAuthErrorImpl _value, $Res Function(_$socialAuthErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$socialAuthErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$socialAuthErrorImpl implements socialAuthError {
  const _$socialAuthErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'SocialAuthState.socialAuthError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$socialAuthErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$socialAuthErrorImplCopyWith<_$socialAuthErrorImpl> get copyWith =>
      __$$socialAuthErrorImplCopyWithImpl<_$socialAuthErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(dynamic data) socialAuthSuccess,
    required TResult Function(String? error) socialAuthError,
  }) {
    return socialAuthError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(dynamic data)? socialAuthSuccess,
    TResult? Function(String? error)? socialAuthError,
  }) {
    return socialAuthError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(dynamic data)? socialAuthSuccess,
    TResult Function(String? error)? socialAuthError,
    required TResult orElse(),
  }) {
    if (socialAuthError != null) {
      return socialAuthError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(googleAuthSuccess value) socialAuthSuccess,
    required TResult Function(socialAuthError value) socialAuthError,
  }) {
    return socialAuthError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult? Function(socialAuthError value)? socialAuthError,
  }) {
    return socialAuthError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(googleAuthSuccess value)? socialAuthSuccess,
    TResult Function(socialAuthError value)? socialAuthError,
    required TResult orElse(),
  }) {
    if (socialAuthError != null) {
      return socialAuthError(this);
    }
    return orElse();
  }
}

abstract class socialAuthError implements SocialAuthState {
  const factory socialAuthError(final String? error) = _$socialAuthErrorImpl;

  String? get error;

  /// Create a copy of SocialAuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$socialAuthErrorImplCopyWith<_$socialAuthErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
