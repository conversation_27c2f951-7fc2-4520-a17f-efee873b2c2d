import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';

sealed class AllChatsState {}

final class AllChatsInitialState extends AllChatsState {}

final class AllChatsLoadingState extends AllChatsState {}

final class AllChatsDataState extends AllChatsState {
  final List<ChatGroupModel> chats;
  AllChatsDataState(this.chats);
}

final class AllChatsErrorState extends AllChatsState {
  final String message;
  AllChatsErrorState(this.message);
}
