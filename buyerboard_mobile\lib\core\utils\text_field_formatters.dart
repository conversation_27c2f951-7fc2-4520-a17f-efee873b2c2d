import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class USPhoneNumberInputFormatter extends TextInputFormatter {
  final MaskTextInputFormatter maskFormatter;

  USPhoneNumberInputFormatter()
      : maskFormatter = MaskTextInputFormatter(
          mask: '+1 (###) ###-####',
          filter: {"#": RegExp(r'[0-9]')},
          type: MaskAutoCompletionType.lazy,
        );

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Ensure that only one '1' is prepended
    String newText = newValue.text;

    // Check if the new text doesn't start with '+1' and is not empty
    if (newText.length == 1 && !newText.startsWith('1')) {
      newText = '+1 ($newText';
    }

    // Apply the mask formatter to format the text
    newValue = newValue.copyWith(text: newText);
    return maskFormatter.formatEditUpdate(oldValue, newValue);
  }
}

class USCurrencyInputFormatter extends TextInputFormatter {
  final String symbol;
  final NumberFormat _currencyFormat;

  USCurrencyInputFormatter({this.symbol = '\$'})
      : _currencyFormat =
            NumberFormat.currency(symbol: symbol, decimalDigits: 0);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }

    // Remove all non-digit characters
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    // Remove leading zeros if more than one character
    if (digitsOnly.length > 1 && digitsOnly.startsWith('0')) {
      digitsOnly = digitsOnly.replaceFirst(RegExp(r'^0+'), '');
    }

    // If digitsOnly is empty after removing leading zeros, set it to '0'
    if (digitsOnly.isEmpty) {
      digitsOnly = '0';
    }

    // If the value is '0', allow it but do not format it
    if (digitsOnly == '0') {
      return TextEditingValue(
        text: digitsOnly,
        selection: TextSelection.collapsed(offset: digitsOnly.length),
      );
    }

    // Parse the number
    int number = int.tryParse(digitsOnly) ?? 0;

    // Format the number
    String formattedValue = _currencyFormat.format(number);

    return TextEditingValue(
      text: formattedValue,
      selection: TextSelection.collapsed(offset: formattedValue.length),
    );
  }
}

class CurrencyTextEditingController extends TextEditingController {
  double _numericValue = 0.0;

  double get numericValue => _numericValue;

  @override
  set value(TextEditingValue newValue) {
    super.value = newValue;
    _updateNumericValue(newValue.text);
  }

  void _updateNumericValue(String text) {
    String digitsOnly = text.replaceAll(RegExp(r'[^\d.]'), '');
    _numericValue = double.tryParse(digitsOnly) ?? 0.0;
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: capitalize(newValue.text),
      selection: newValue.selection,
    );
  }
}

String capitalize(String value) {
  if (value.trim().isEmpty) return "";
  return "${value[0].toUpperCase()}${value.substring(1)}";
}
