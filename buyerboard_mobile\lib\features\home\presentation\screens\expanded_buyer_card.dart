import 'package:buyer_board/common/widgets/profile_avatar.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/extensions/date_time_extension.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filter_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filtered_value_cubit.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:buyer_board/features/home/<USER>/widgets/bb_chip.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
// ignore: depend_on_referenced_packages
import 'package:get/utils.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/resources/text_styles.dart';
import '../../../favorites/presentation/cubit/favourite_buyers_cubit.dart';

class ExpandedBuyerCard extends StatefulWidget {
  const ExpandedBuyerCard(
      {super.key, required this.buyer, this.showChatWithMe = true});
  final BuyerModel buyer;
  final bool showChatWithMe;

  @override
  State<ExpandedBuyerCard> createState() => _ExpandedBuyerCardState();
}

class _ExpandedBuyerCardState extends State<ExpandedBuyerCard> {
  bool loading = false;
  late BuyerModel _buyer;
  // Color _color = AppColors.primary;
  String getBackgroundImage(String propertyType) {
    return 'assets/images/png/bg_${widget.buyer.myBuyer ? propertyType : '${propertyType}_dark'}.png';
  }

  Widget getFinancialStatusChip(String financialStatus) {
    switch (financialStatus) {
      case "pre_qualified":
        return const BBChip.preQualified();
      case "pre_approved":
        return const BBChip.preApproved();
      case "all_cash":
        return const BBChip.allCash();
      default:
        return const BBChip.notApproved(
          labelColor: AppColors.greyLight,
        );
    }
  }

  Widget getBrokerAgreementStatusChip() {
    final today = DateTime.now();
    final isExpired =
        widget.buyer.buyerExpirationDate?.isBefore(today) ?? false;
    final values = isExpired
        ? (color: AppColors.success, label: "Expired")
        : (color: AppColors.error, label: "Active");
    return BBChip(
      label: values.label,
      backgroundColor: values.color,
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 2,
      ),
      borderRadius: BorderRadius.circular(4),
    );
  }

  void onFavouriteToggle() async {
    final isFav = _buyer.isFavourite;
    setState(() {
      loading = true;
    });
    context
        .read<FavouriteBuyersCubit>()
        .toggleFavourite(buyerId: widget.buyer.id!, isFavourite: !isFav)
        .then((val) {
      setState(() {
        loading = false;
        _buyer = _buyer.copyWith(isFavourite: !isFav);
        context.read<BuyersBloc>().add(
            UpdateBuyer(buyer: widget.buyer.copyWith(isFavourite: !isFav)));
      });
      context.read<FavouriteBuyersCubit>().getFavouriteBuyers(silent: true);
      context.read<BuyersBloc>().add(LoadBuyers(context: context));
    }).onError((error, _) {
      setState(() {
        loading = false;
        _buyer = _buyer.copyWith(isFavourite: isFav);
        context
            .read<BuyersBloc>()
            .add(UpdateBuyer(buyer: widget.buyer.copyWith(isFavourite: isFav)));
      });
    });
  }

  // String getBudget() {
  //   // final budget =
  //   //     double.parse(widget.buyer.buyerNeeds?.budget ?? '0').formatCurrency();
  //   final budget = double.tryParse(widget.buyer.buyerNeeds?.budget ?? '0') ?? 0;
  //   final formattedValue = CoreUtils.formatBudgetAmount(budget);
  //   final isRent = widget.buyer.buyerNeeds?.purchaseTypeStr == "rent";
  //   return isRent ? "$formattedValue/mo" : formattedValue;
  // }

  @override
  void initState() {
    _buyer = widget.buyer;
    // if (!widget.buyer.myBuyer) {
    //   _color = AppColors.black;
    // }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    final color = _buyer.myBuyer ? colors.primary : colors.inverseSurface;
    final foregroundColor =
        _buyer.myBuyer ? colors.onPrimary : colors.onInverseSurface;
    final expirationDate = _buyer.buyerExpirationDate;
    final currentDate = DateTime.now();
    final expirationDays = (expirationDate != null)
        ? DateTime(
                expirationDate.year, expirationDate.month, expirationDate.day)
            .difference(
              DateTime(
                currentDate.year,
                currentDate.month,
                currentDate.day,
              ),
            )
            .inDays
        : 0;

    return Scaffold(
      appBar: AppBar(
        title: BlocBuilder<BuyerFilterLocationsCubit, BuyerLocationFilterState>(
          builder: (context, state) {
            final locationState = context.read<CurrentLocationCubit>().state;
            LocationEntity? currentLocation;
            if (locationState is LocationDataState) {
              currentLocation = locationState.location;
            }
            return switch (state) {
              BuyerLocationFilterLoaded(locations: final data) =>
                data.isEmpty || widget.buyer.buyerLocationsOfInterest.isEmpty
                    ? const SizedBox()
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.buyer.buyerLocationsOfInterest.length > 1
                                ? currentLocation?.zipCode ??
                                    widget.buyer.buyerLocationsOfInterest.first
                                : widget.buyer.buyerLocationsOfInterest.first,
                            style: context.typography.largeSemi.copyWith(
                              color: context.appColors.whitePXLight,
                            ),
                          ),
                          spacerW8,
                          Text(
                            _zipCodeToCity(
                                widget.buyer.buyerLocationsOfInterest.length > 1
                                    ? currentLocation?.zipCode ??
                                        widget.buyer.buyerLocationsOfInterest
                                            .first
                                    : widget
                                        .buyer.buyerLocationsOfInterest.first,
                                data),
                            style: context.typography.largeReg.copyWith(
                              color: context.appColors.pLight,
                            ),
                          ),
                        ],
                      ),
              BuyerLocationFilterError(message: final message) =>
                Text(message.toString()),
              _ => const CupertinoActivityIndicator(),
            };
          },
        ),
        iconTheme: const IconThemeData(
          color: AppColors.white,
        ),
        actions: [
          if (widget.buyer.myBuyer)
            TextButton(
              onPressed: () => context.push(
                PagePath.editBuyer,
                extra: widget.buyer,
              ),
              child: Text(
                "Edit",
                style: context.typography.largeReg.copyWith(
                  color: context.appColors.whitePXLight,
                ),
              ),
            )
        ],
      ),
      body: SafeArea(
          child: SingleChildScrollView(
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            Column(
              children: [
                Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Hero(
                      tag: 'property_image${widget.buyer.id!}',
                      child: Image.asset(
                        // getBackgroundImage(widget.buyer.buyerNeeds!.propertyType!),
                        widget.buyer.propertyImage,
                        height: 224,
                        width: double.maxFinite,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Container(
                        height: 90,
                        width: double.maxFinite,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              (_buyer.myBuyer
                                      ? context.appColors.pPXLight
                                      : context.appColors.gBlackGLight)
                                  .withOpacity(0),
                              (_buyer.myBuyer
                                      ? context.appColors.pPXLight
                                      : context.appColors.gBlackGLight)
                                  .withOpacity(0.1),
                              (_buyer.myBuyer
                                      ? context.appColors.pPXLight
                                      : context.appColors.gBlackGLight)
                                  .withOpacity(0.3),
                              _buyer.myBuyer
                                  ? context.appColors.pPXLight
                                  : context.appColors.gBlackGLight,
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: const [0, 0.2, 0.5, 1],
                            tileMode: TileMode.clamp,
                          ),
                        ))
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ColoredBox(
                      color: color,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: Dimensions.materialPadding,
                          right: Dimensions.materialPadding,
                          top: 12,
                          bottom: 4,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              widget.buyer.sku ?? 'N/A',
                              style: context.typography.large1xBlack.copyWith(
                                color: foregroundColor,
                              ),
                            ),
                            spacerH4,
                            _BuyerCardCreated(
                                foregroundColor: foregroundColor,
                                widget: widget),
                            if (_buyer.myBuyer) ...[
                              spacerH8,
                              _BuyerExpiration(
                                  foregroundColor: foregroundColor,
                                  widget: widget,
                                  expirationDays: expirationDays),
                            ],
                            spacerH16,
                            _ChipsGridView(buyer: _buyer),
                          ],
                        ),
                      ),
                    ),
                    _NeedsDetailsTile(buyer: _buyer),
                  ],
                ),
                if (widget.buyer.buyerLocationsOfInterest.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Dimensions.materialPadding,
                    ),
                    child: ZipCodeSection(
                      label: "Locations Of Interest:",
                      values: widget.buyer.buyerLocationsOfInterest,
                      color: color,
                      foregroundColor: foregroundColor,
                    ),
                  ),
                if (widget.buyer.additionalRequests.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Dimensions.materialPadding,
                    ).copyWith(top: 12),
                    child: AttributesListingWdget(
                      label: "Additional Requests:",
                      values: widget.buyer.additionalRequests,
                      color: color,
                      foregroundColor: foregroundColor,
                    ),
                  ),
                const SizedBox(height: 12),
                if (widget.buyer.myBuyer)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Dimensions.materialPadding,
                    ),
                    child: BuyerContactDetailsWidget(
                      name:
                          '${widget.buyer.firstName ?? ""} ${widget.buyer.lastName ?? ""}',
                      primaryContactNumber:
                          widget.buyer.primaryPhoneNumber ?? '',
                      emailAddress: widget.buyer.email ?? '',
                      creationDate:
                          widget.buyer.insertedAt!.formatRelativeDateTime(),
                      color: color,
                      foregroundColor: foregroundColor,
                      isMyBuyer: widget.buyer.myBuyer,
                    ),
                  ),
                if (!widget.buyer.myBuyer)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Dimensions.materialPadding,
                    ),
                    child: AgentContactDetailsWidget(
                      showChatWithMe: widget.showChatWithMe,
                      buyer: widget.buyer,
                      name:
                          '${widget.buyer.user?.profile?.firstName ?? ""} ${widget.buyer.user?.profile?.lastName ?? ""}',
                      brokerageName:
                          widget.buyer.user?.profile?.brokerageName ?? '',
                      avatar: widget.buyer.user?.profile?.imageUrl ?? '',
                      primaryContactNumber:
                          widget.buyer.user?.profile?.primaryPhoneNumber ?? '',
                      emailAddress:
                          widget.buyer.user?.profile?.agentEmail ?? '',
                      creationDate:
                          widget.buyer.insertedAt!.formatRelativeDateTime(),
                      color: color,
                      foregroundColor: foregroundColor,
                    ),
                  ),
                const SizedBox(height: 32),
              ],
            ),
            Positioned(
              child: Container(
                width: 46,
                height: 46,
                decoration: BoxDecoration(
                  color: AppColors.black.withOpacity(0.2),
                  borderRadius: const BorderRadiusDirectional.only(
                    bottomStart: Radius.circular(12),
                  ),
                ),
                child: InkWell(
                  onTap: onFavouriteToggle,
                  child: Center(
                    child: loading
                        ? CupertinoActivityIndicator(
                            color: colors.onPrimaryFixed,
                          )
                        : Icon(
                            _buyer.isFavourite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color: colors.onPrimaryFixed,
                          ),
                  ),
                ),
              ),
            ),
          ],
        ),
      )),
    );
  }
}

class _ChipsGridView extends StatelessWidget {
  const _ChipsGridView({
    super.key,
    required BuyerModel buyer,
  }) : _buyer = buyer;

  final BuyerModel _buyer;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            if (_buyer.buyerNeeds?.budget != null)
              Expanded(
                child: _CardItem(
                  myBuyer: _buyer.myBuyer,
                  label:
                      'Up To: \$${CoreUtils.formatLargeNumber(double.tryParse(_buyer.buyerNeeds?.budget ?? '0') ?? 0)}',
                ),
              ),
            spacerW8,
            Expanded(
              child: _CardItem(
                myBuyer: _buyer.myBuyer,
                label: _buyer.buyerNeeds?.propertyType?.label,
              ),
            ),
          ],
        ),
        spacerH8,
        Row(
          children: [
            if (_buyer.buyerNeeds?.budget != null)
              Expanded(
                child: _CardItem(
                  myBuyer: _buyer.myBuyer,
                  label: _buyer.buyerNeeds?.financialStatus?.label,
                ),
              ),
            spacerW8,
            Expanded(
              child: _CardItem(
                myBuyer: _buyer.myBuyer,
                label: _buyer.buyerNeeds?.purchaseType?.label,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _BuyerExpiration extends StatelessWidget {
  const _BuyerExpiration({
    super.key,
    required this.foregroundColor,
    required this.widget,
    required this.expirationDays,
  });

  final Color foregroundColor;
  final ExpandedBuyerCard widget;
  final int expirationDays;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Buyer Expiration: ',
          style: context.typography.smallBlack.copyWith(color: foregroundColor),
        ),
        Text(
          DateFormat(Constants.dateFormatter)
              .format(widget.buyer.buyerExpirationDate!),
          style: context.typography.smallReg.copyWith(color: foregroundColor),
        ),
        spacerW4,
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: expirationDays < 0 ? AppColors.error : AppColors.success,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          child: Text(
            expirationDays < 0 ? 'Expired' : '$expirationDays DAYS',
            style:
                context.typography.small1xSemi.copyWith(color: AppColors.white),
          ),
        )
      ],
    );
  }
}

class _BuyerCardCreated extends StatelessWidget {
  const _BuyerCardCreated({
    super.key,
    required this.foregroundColor,
    required this.widget,
  });

  final Color foregroundColor;
  final ExpandedBuyerCard widget;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'BuyerCard Created: ',
          style: context.typography.smallBlack.copyWith(color: foregroundColor),
        ),
        Text(
          DateFormat(Constants.dateFormatter)
              .format(widget.buyer.insertedAt!.toLocal()),
          style: context.typography.smallReg.copyWith(color: foregroundColor),
        ),
      ],
    );
  }
}

class _NeedsDetailsTile extends StatelessWidget {
  const _NeedsDetailsTile({
    super.key,
    required BuyerModel buyer,
  }) : _buyer = buyer;

  final BuyerModel _buyer;

  @override
  Widget build(BuildContext context) {
    final myBuyer = _buyer.myBuyer;
    final backgroudColor =
        myBuyer ? context.appColors.pPXLight : context.appColors.gBlackGLight;

    final foregroundColor =
        myBuyer ? context.appColors.whitePDark : context.appColors.whiteBlack;
    final isLand = _buyer.buyerNeeds?.propertyType == PropertyType.land;
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 24,
        vertical: 20,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            backgroudColor,
            context.appColors.whiteBlack,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0, 1],
          tileMode: TileMode.clamp,
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: backgroudColor,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (!isLand) ...[
              _item(
                context,
                _buyer.buyerNeeds!.minBedrooms.toString() ?? "0",
                Drawables.icBedroomOutlined,
                foregroundColor,
              ),
              _verticalDivider(context, foregroundColor),
            ],
            if (!isLand) ...[
              _item(
                context,
                _buyer.buyerNeeds!.minBathrooms.toString() ?? "0",
                Drawables.icBathroomOutlined,
                foregroundColor,
              ),
              _verticalDivider(context, foregroundColor),
            ],
            _item(
              context,
              CoreUtils.formatLargeNumber(_buyer.buyerNeeds!.minArea ?? 0),
              Drawables.icAreaOutlined,
              foregroundColor,
            ),
          ],
        ),
      ),
    );
  }

  Container _verticalDivider(BuildContext context, Color color) {
    return Container(
      width: 1,
      height: 25,
      color: color,
    );
  }

  Row _item(BuildContext context, String title, String iconUrl, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          iconUrl,
          colorFilter: ColorFilter.mode(
            color,
            BlendMode.srcIn,
          ),
        ),
        spacerW12,
        Text(
          title,
          style: context.typography.largeSemi.copyWith(
            color: color,
          ),
        ),
      ],
    );
  }
}

class _CardItem extends StatelessWidget {
  const _CardItem({
    required this.label,
    required this.myBuyer,
  });

  final String? label;
  final bool myBuyer;

  @override
  Widget build(BuildContext context) {
    final backgroundColor = context.appColors.whitePDark;
    final foregroundColor =
        myBuyer ? context.appColors.pPXLight : context.appColors.blackWhite;
    if (label == null) {
      return const SizedBox.shrink();
    }
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100),
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: context.appColors.black.withOpacity(0.3),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      child: Text(
        label!,
        style: context.typography.mediumSemi.copyWith(
          color: foregroundColor,
        ),
      ),
    );
  }
}

class BuyerContactDetailsWidget extends StatelessWidget {
  const BuyerContactDetailsWidget({
    super.key,
    required this.name,
    required this.primaryContactNumber,
    required this.emailAddress,
    required this.creationDate,
    required this.color,
    required this.foregroundColor,
    required this.isMyBuyer,
  });
  final String name;
  final String primaryContactNumber;
  final String emailAddress;
  final String creationDate;
  final Color color;
  final Color foregroundColor;
  final bool isMyBuyer;

  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((MapEntry<String, String> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  void onLaunchEmail() async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: emailAddress,
      query: encodeQueryParameters(<String, String>{
        'subject': 'A Message from Your Agent and BuyerBoard.',
      }),
    );
    await launchUrl(emailLaunchUri);
  }

  void onLaunchPhone() async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: primaryContactNumber,
    );
    await launchUrl(launchUri);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: 4,
          color: color,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.maxFinite,
            padding: const EdgeInsets.symmetric(
                horizontal: Dimensions.padding_24,
                vertical: Dimensions.padding_8),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              color: color,
            ),
            child: Text(
              "Contact Details ${isMyBuyer ? "(My Buyer)" : ""}:",
              style: context.typography.largeReg.copyWith(
                color: foregroundColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(Dimensions.materialPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: context.typography.largeBlack.copyWith(color: color),
                ),
                const SizedBox(height: 8),
                if (primaryContactNumber.isNotEmpty)
                  FilledButton.icon(
                    style: FilledButton.styleFrom(
                      foregroundColor: foregroundColor,
                      backgroundColor: color,
                      fixedSize: const Size(double.maxFinite, 44),
                    ),
                    onPressed: onLaunchPhone,
                    label: Text(
                      primaryContactNumber,
                      style: context.typography.mediumSemi.copyWith(
                        color: foregroundColor,
                      ),
                    ),
                    icon: SvgPicture.asset(
                      Drawables.icPhone,
                      height: 20,
                      colorFilter: ColorFilter.mode(
                        foregroundColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                if (emailAddress.isNotEmpty)
                  FilledButton.icon(
                    style: FilledButton.styleFrom(
                      foregroundColor: foregroundColor,
                      backgroundColor: color,
                      fixedSize: const Size(double.maxFinite, 44),
                    ),
                    onPressed: onLaunchEmail,
                    label: Text(
                      emailAddress,
                      style: context.typography.mediumSemi
                          .copyWith(color: foregroundColor),
                    ),
                    icon: SvgPicture.asset(
                      Drawables.icMail,
                      color: foregroundColor,
                    ),
                  ),
                const SizedBox(height: 8),
                Align(
                  child: Text(
                    'This information is confidential',
                    style: context.typography.smallReg.copyWith(
                      color: context.appColors.greyM,
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class AgentContactDetailsWidget extends StatelessWidget {
  const AgentContactDetailsWidget({
    super.key,
    required this.name,
    required this.brokerageName,
    required this.primaryContactNumber,
    required this.emailAddress,
    required this.avatar,
    required this.creationDate,
    required this.color,
    required this.foregroundColor,
    required this.buyer,
    required this.showChatWithMe,
  });
  final String name;
  final String brokerageName;
  final String avatar;
  final String primaryContactNumber;
  final String emailAddress;
  final String creationDate;
  final Color color;
  final Color foregroundColor;
  final BuyerModel buyer;
  final bool showChatWithMe;

  @override
  Widget build(BuildContext context) {
    final firstCharsOfName =
        name.trim().isEmpty ? '' : name.split(' ').map((e) => e[0]).join('');
    final profilePlaceHolder =
        firstCharsOfName.trim().isNotEmpty ? firstCharsOfName : 'User';
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: 4,
          color: color,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.maxFinite,
            padding: const EdgeInsets.symmetric(
                horizontal: Dimensions.padding_24,
                vertical: Dimensions.padding_8),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              color: color,
            ),
            child: Text(
              "Agent Details",
              style: AppStyles.large.copyWith(
                color: foregroundColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(Dimensions.materialPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // CircleAvatar(radius: 28, backgroundColor: color),
                    ProfileAvatar(
                      imageUrl: avatar,
                      avatarPlaceHolderTitle: profilePlaceHolder,
                      textStyle: context.typography.largeSemi.copyWith(
                        color: foregroundColor,
                      ),
                      radius: 28,
                      backgroundColor: color,
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Text(
                          //   "Brokers's Name",
                          //   style: AppStyles.large.copyWith(
                          //     color: color,
                          //     fontSize: 20,
                          //     fontWeight: FontWeight.w800,
                          //   ),
                          // ),
                          if (name.trim().isNotEmpty)
                            Text(
                              name,
                              // style: AppStyles.medium.copyWith(color: color),
                              style: context.typography.largeBlack.copyWith(
                                color: color,
                              ),
                            ),
                          if (brokerageName.trim().isNotEmpty)
                            Text(
                              brokerageName,
                              style: context.typography.mediumReg.copyWith(
                                color: color,
                              ),
                            ),
                          if (primaryContactNumber.trim().isNotEmpty)
                            Text(
                              primaryContactNumber,
                              style: context.typography.mediumReg.copyWith(
                                color: color,
                              ),
                            ),
                          if (emailAddress.trim().isNotEmpty)
                            Text(
                              emailAddress,
                              style: context.typography.mediumReg.copyWith(
                                color: color,
                              ),
                            )
                        ],
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 8),
                if (showChatWithMe)
                  FilledButton.icon(
                    style: FilledButton.styleFrom(
                      foregroundColor: foregroundColor,
                      backgroundColor: color,
                      fixedSize: const Size(double.maxFinite, 44),
                    ),
                    onPressed: () {
                      final appPreferences = Injector.resolve<AppPreferences>();
                      final userSessionCubit =
                          Injector.resolve<UserSessionCubit>();
                      final user =
                          appPreferences.getUser() ?? userSessionCubit.state;
                      final userId = user?.id;
                      if (userId == null) {
                        return;
                      }

                      final chatBuyer = ChatGroupModel(
                        id: null,
                        buyerId: buyer.id ?? 0,
                        myBuyer: buyer.myBuyer,
                        firstName: buyer.firstName ?? "",
                        lastName: buyer.lastName ?? "",
                        buyersAlias: buyer.buyersAlias ?? "",
                        sku: buyer.sku ?? "",
                        threads: [
                          ChatGroupThreadModel(
                            user: buyer.user!,
                            userId: userId,
                            agentImageUrl: avatar,
                            userImageUrl: user?.profile?.imageUrl,
                            id: null,
                            agentId: buyer.user?.id ?? -1,
                            avatarUrl: buyer.user?.profile?.imageUrl,
                            name: buyer.user?.profile?.firstName,
                            lastMessage: null,
                            timeStamp: "",
                            hasNewMessage: false,
                          )
                        ],
                      );

                      final extra =
                          (buyer: chatBuyer, agent: chatBuyer.threads.first);
                      context.pushNamed(PagePath.chatDetailsScreen,
                          extra: extra);
                    },
                    label: Text(
                      'Chat With Me',
                      style: context.typography.mediumSemi
                          .copyWith(color: foregroundColor),
                    ),
                    icon: Image.asset(
                      Drawables.chatIcon,
                      height: 20,
                      color: foregroundColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class AttributesListingWdget extends StatelessWidget {
  const AttributesListingWdget({
    super.key,
    required this.label,
    required this.values,
    required this.color,
    required this.foregroundColor,
  });
  final String label;
  final List<String> values;
  final Color color;
  final Color foregroundColor;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: 4,
          color: color,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.maxFinite,
            padding: const EdgeInsets.symmetric(
                horizontal: Dimensions.padding_24,
                vertical: Dimensions.padding_8),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              color: color,
            ),
            child: Text(
              label,
              style: context.typography.largeReg.copyWith(
                color: foregroundColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(Dimensions.materialPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: values.map(
                (value) {
                  return Text(
                    "  •   $value",
                    style: context.typography.mediumSemi.copyWith(
                      color: color,
                    ),
                  );
                },
              ).toList(),
            ),
          )
        ],
      ),
    );
  }
}

class ZipCodeSection extends StatelessWidget {
  const ZipCodeSection({
    super.key,
    required this.label,
    required this.values,
    required this.color,
    required this.foregroundColor,
  });
  final String label;
  final List<String> values;
  final Color color;
  final Color foregroundColor;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: 4,
          color: color,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.maxFinite,
            padding: const EdgeInsets.symmetric(
                horizontal: Dimensions.padding_24,
                vertical: Dimensions.padding_8),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              color: color,
            ),
            child: Text(
              label,
              style: context.typography.largeReg.copyWith(
                color: foregroundColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(Dimensions.materialPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: values.map(
                (value) {
                  return BlocBuilder<BuyerFilterLocationsCubit,
                      BuyerLocationFilterState>(
                    // future: _location(value, context),
                    builder: (context, state) {
                      return switch (state) {
                        BuyerLocationFilterLoaded(locations: final data) => Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "  •   $value ",
                                style: context.typography.mediumSemi.copyWith(
                                  color: color,
                                ),
                              ),
                              spacerW8,
                              Text(
                                _zipCodeToCity(value, data),
                                style: context.typography.mediumReg.copyWith(
                                  color: color,
                                ),
                              ),
                            ],
                          ),
                        BuyerLocationFilterError(message: final message) =>
                          Text(message.toString()),
                        _ => const CupertinoActivityIndicator(),
                      };
                    },
                  );
                },
              ).toList(),
            ),
          )
        ],
      ),
    );
  }

  // Future<String> _location(String zipCode, BuildContext context) async {
  //   final state = context.read<BuyerFilterLocationsCubit>().state;
  //   final response = switch (state) {
  //     BuyerLocationFilterLoaded(locations: final data) =>
  //       (data.firstWhereOrNull(
  //         (element) => element.zipCode == zipCode,
  //       ))?.cityName,
  //     _ => '',
  //   };
  //   return response ?? '';
  // }
}

String _zipCodeToCity(String zipCode, List<LocationEntity> locations) {
  final location = locations.firstWhereOrNull(
    (element) => element.zipCode == zipCode,
  );
  return location?.cityName ?? '';
}
