import 'package:buyer_board/common/widgets/common_radio_tile.dart';
import 'package:buyer_board/core/extensions/theme_extensions.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/features/settings/presentation/cubit/theme_cubit.dart';
import 'package:buyer_board/features/settings/presentation/widgets/setting_section_base_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SettingsThemeSection extends StatefulWidget {
  const SettingsThemeSection({super.key});

  @override
  State<SettingsThemeSection> createState() => _SettingsThemeSectionState();
}

class _SettingsThemeSectionState extends State<SettingsThemeSection> {
  @override
  Widget build(BuildContext context) {
    final List<ThemeMode> sortedValues = [
      ThemeMode.light,
      ThemeMode.dark,
      ThemeMode.system,
    ];
    return SettingSectionBaseWidget(
      title: Strings.appearance,
      subtitle: Strings.appearanceSubtitle,
      child: MediaQuery.removePadding(
        context: context,
        removeTop: true,
        removeBottom: true,
        child: BlocBuilder<ThemeCubit, ThemeMode>(builder: (_, state) {
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: ThemeMode.values.length,
            itemBuilder: (context, index) {
              return CommonRadioTile<ThemeMode>(
                label: sortedValues[index].label,
                value: sortedValues[index],
                groupValue: state,
                onChange: (value) {
                  context.read<ThemeCubit>().update(sortedValues[index]);
                },
              );
            },
          );
        }),
      ),
    );
  }
}
