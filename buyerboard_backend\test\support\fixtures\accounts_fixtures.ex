defmodule BuyerboardBackend.AccountsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `BuyerboardBackend.Accounts` context.
  """

  def unique_user_email, do: "user#{System.unique_integer()}@example.com"
  def valid_user_password, do: "Media2002,"

  # Updated valid_user_attributes to include first_name and last_name
  def valid_user_attributes(attrs \\ %{}) do
    generated_attrs =  Enum.into(attrs, %{
      email: unique_user_email(),
      password: valid_user_password(),
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>"
    })
  end

  # Updated user_fixture to use the updated attributes
  def user_fixture(attrs \\ %{}) do

    {:ok, user} =
      attrs
      |> valid_user_attributes()
      |> BuyerboardBackend.Accounts.register_user()

    user
  end

  def extract_user_token(fun) do
    {:ok, captured_email} = fun.(&"[TOKEN]#{&1}[TOKEN]")
    [_, token | _] = String.split(captured_email.text_body, "[TOKEN]")
    token
  end
end
