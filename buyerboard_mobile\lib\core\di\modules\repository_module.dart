import 'package:buyer_board/features/add_buyer/data/repository/buyer_repository_impl.dart';
import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';
import 'package:buyer_board/features/auth/data/repository/auth_repository_impl.dart';
import 'package:buyer_board/features/auth/domain/repository/auth_repository.dart';
import 'package:buyer_board/features/buyers_location_filter/data/repositories/buyer_location_filter_repository_impl.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/repositories/buyer_location_filter_repository.dart';
import 'package:buyer_board/features/chat/data/repositories/all_chats_repository_impl.dart';
import 'package:buyer_board/features/chat/data/repositories/archived_chat_repository_impl.dart';
import 'package:buyer_board/features/chat/data/repositories/chat_notifications_repository_impl.dart';
import 'package:buyer_board/features/chat/data/repositories/chat_repository_impl.dart';
import 'package:buyer_board/features/chat/data/datasources/chat_websocket_service.dart';
import 'package:buyer_board/features/chat/domain/repositories/all_chats_repository.dart';
import 'package:buyer_board/features/chat/domain/repositories/archived_chat_repository.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_notification_repository.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_repository.dart';
import 'package:buyer_board/features/filters/data/filters_respository.dart';
import 'package:buyer_board/features/forget_password/domain/repository/forget_password_repository.dart';
import 'package:buyer_board/features/profile/data/repository/user_repository_impl.dart';
import 'package:buyer_board/features/profile/domain/repository/user_repository.dart';
import 'package:buyer_board/features/settings/data/datasources/settings_data_source.dart';
import 'package:buyer_board/features/settings/data/repositories/settings_repository_impl.dart';
import 'package:buyer_board/features/settings/domain/repositories/settings_repository.dart';
import 'package:kiwi/kiwi.dart';
import '../../../features/forget_password/data/repository/forget_password_repository_impl.dart';

abstract class RepositoryModule {
  static late KiwiContainer _container;

  static void setup(KiwiContainer container) {
    _container = container;
    _setupAuthRepository();
    _setupResetPasswordRepository();
    _setupUserRepository();
    _setupBuyerRepository();
    _setupFiltersRepository();
    _setupBuyerFilterLocationRepository();
    _setupChatRepositoriesAndSockets();
    _setupSettingsRepository();
  }

  static void _setupSettingsRepository() {
    _container.registerSingleton<SettingsDataSource>(
      (_) => SettingsDataSourceImpl(
        restAPIClient: _container.resolve(),
      ),
    );
    _container.registerSingleton<SettingsRepository>(
      (_) => SettingsRepositoryImpl(
        dataSource: _container.resolve(),
      ),
    );
  }

  static void _setupAuthRepository() {
    _container.registerSingleton<AuthRepository>(
      (_) => AuthRepositoryImpl(
        restAPIClient: _container.resolve(),
      ),
    );
  }

  static void _setupResetPasswordRepository() {
    _container.registerSingleton<ResetPasswordRepository>(
      (_) => ResetPasswordRepositoryImpl(
        restAPIClient: _container.resolve(),
      ),
    );
  }

  static void _setupUserRepository() {
    _container.registerSingleton<UserRepository>(
      (_) => UserRepositoryImpl(
        restAPIClient: _container.resolve(),
      ),
    );
  }

  static void _setupBuyerRepository() {
    _container.registerSingleton<BuyerRepository>(
      (_) => BuyerRepositoryImpl(
        restAPIClient: _container.resolve(),
      ),
    );
  }

  static void _setupFiltersRepository() {
    _container.registerSingleton<FiltersRepository>(
      (_) => FiltersRepositoryImpl(
        restAPIClient: _container.resolve(),
      ),
    );
  }

  static void _setupBuyerFilterLocationRepository() {
    _container.registerSingleton<BuyerLocationFilterRepository>(
      (_) => BuyerLocationFilterRepositoryImpl(
        restAPIClient: _container.resolve(),
      ),
    );
  }

  // Setup All Repositories and Sockets for Chat
  static void _setupChatRepositoriesAndSockets() {
    _container.registerSingleton<WebSocketService>(
      (_) => FlutterWebSocketService(),
      name: 'chatsSocket',
    );
    _container.registerSingleton<WebSocketService>(
      (_) => FlutterWebSocketService(),
      name: 'notificationsSocket',
    );
    _container.registerSingleton<WebSocketService>(
      (_) => FlutterWebSocketService(),
      name: 'allChatsSocket',
    );

    _container.registerSingleton<WebSocketService>(
      (_) => FlutterWebSocketService(),
      name: 'archivedChatsSocket',
    );

    _container.registerSingleton<ChatRepository>(
      (_) => ChatRepositoryImpl(
        _container<WebSocketService>('chatsSocket'),
        _container.resolve(),
      ),
    );

    _container.registerSingleton<AllChatsRepository>(
      (_) => AllChatsRepositoryImpl(
        _container<WebSocketService>('allChatsSocket'),
      ),
    );

    _container.registerSingleton<ChatNotificationsRepository>(
      (_) => ChatNotificationsRepositoryImpl(
        _container<WebSocketService>('notificationsSocket'),
      ),
    );

    _container.registerSingleton<ArchivedChatRepository>(
      (_) => ArchivedChatRepositoryImpl(
        _container<WebSocketService>('archivedChatsSocket'),
      ),
    );
  }
}
