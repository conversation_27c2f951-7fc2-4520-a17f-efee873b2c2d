import 'package:buyer_board/features/chat/domain/repositories/chat_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/web.dart';

class ChatSocketConnectionCubit extends Cubit<ChatSocketState> {
  ChatSocketConnectionCubit({
    required this.repository,
  }) : super(ChatSocketInitial());

  final ChatRepository repository;
  void connect() async {
    emit(ChatSocketConnecting());
    try {
      repository.initializeConnection();
      emit(ChatSocketConnected());
    } catch (e) {
      emit(ChatSocketConnectionError(e.toString()));
    }
  }

  void disconnect() {
    try {
      repository.closeConnection();

      emit(ChatSocketDisconnected());
    } catch (e) {
      emit(ChatSocketConnectionError(e.toString()));
    }
  }
}

// web_socket_state.dart
sealed class ChatSocketState {}

class ChatSocketInitial extends ChatSocketState {
  ChatSocketInitial() {
    Logger().f('ChatSocketInitial');
  }
}

class ChatSocketConnecting extends ChatSocketState {
  ChatSocketConnecting() {
    Logger().f('ChatSocketConnecting');
  }
}

class ChatSocketConnected extends ChatSocketState {
  ChatSocketConnected() {
    Logger().f('ChatSocketConnected');
  }
}

class ChatSocketConnectionError extends ChatSocketState {
  final String error;

  ChatSocketConnectionError(this.error) {
    Logger().e('ChatSocketConnectionError: $error');
  }
}

class ChatSocketDisconnected extends ChatSocketState {
  ChatSocketDisconnected() {
    Logger().d('ChatSocketDisconnected');
  }
}
