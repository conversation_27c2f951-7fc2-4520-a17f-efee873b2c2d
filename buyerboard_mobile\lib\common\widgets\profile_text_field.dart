import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'common_text_form_field.dart';

class ProfileTextField extends StatelessWidget {
  const ProfileTextField({
    super.key,
    required this.controller,
    this.readOnly = false,
    this.validator,
    this.label,
    this.hint,
    this.errorText,
    this.keyboardType,
    this.onChanged,
    this.onClear,
    this.onTap,
    this.inputFormatters,
    this.prefixIcon,
    this.textCapitalization = TextCapitalization.none,
  });

  final TextEditingController controller;
  final bool readOnly;
  final String? Function(String?)? validator;
  final String? label;
  final String? hint;
  final String? errorText;
  final TextInputType? keyboardType;
  final dynamic Function(String?)? onChanged;
  final VoidCallback? onClear;
  final VoidCallback? onTap;
  final Widget? prefixIcon;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization textCapitalization;

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final xColors = context.appColors;
    final colorScheme = context.colorScheme;
    return CommonTextFormField(
      onTap: onTap,
      errorText: errorText,
      prefixIcon: prefixIcon,
      textCapitalization: textCapitalization,
      controller: controller,
      validator: validator,
      fillColor: Colors.transparent,
      keyboardType: keyboardType,
      onChanged: onChanged,
      label: label,
      inputFormatters: inputFormatters,
      hint: hint,
      readOnly: readOnly,
      labelStyle: typography.smallReg.copyWith(
        color: xColors.greyM,
        overflow: TextOverflow.ellipsis,
      ),
      hintTextStyle: typography.largeReg.copyWith(
        color: xColors.greyLightGreyDefault,
      ),
      contextTextStyle: typography.largeReg.copyWith(
        color: xColors.blackWhite,
      ),
      padding: const EdgeInsets.symmetric(vertical: 8),
      borderColor: colorScheme.outline,
      suffixIcon: _ClearIconButton(
        controller: controller,
        onClear: onClear,
        readOnly: readOnly,
      ),
    );
  }
}

class _ClearIconButton extends StatefulWidget {
  const _ClearIconButton({
    required this.controller,
    required this.readOnly,
    this.onClear,
  });
  final TextEditingController controller;
  final bool readOnly;
  final VoidCallback? onClear;

  @override
  State<_ClearIconButton> createState() => _ClearIconButtonState();
}

class _ClearIconButtonState extends State<_ClearIconButton> {
  bool isEmpty = true;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(() {
      if (mounted) {
        setState(() {
          isEmpty = widget.controller.text.isEmpty;
        });
      }
    });
  }

  @override
  void dispose() {
    widget.controller.removeListener(() {});
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.readOnly || isEmpty
        ? const SizedBox.shrink()
        : GestureDetector(
            onTap: () {
              widget.controller.clear();
              widget.onClear?.call();
            },
            child: const Icon(Icons.clear),
          );
  }
}
