import 'package:buyer_board/features/profile/data/models/response/user_address_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'address_state.freezed.dart';

@freezed
class AddressState with _$AddressState {
  const factory AddressState.initial() = initial;
  const factory AddressState.loading() = loading;
  const factory AddressState.success(
      {required UserAddress address, String? message}) = success;
  const factory AddressState.addressError(String? error) = loginError;
}
