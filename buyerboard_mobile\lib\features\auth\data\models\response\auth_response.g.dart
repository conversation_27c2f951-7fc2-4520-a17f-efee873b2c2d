// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: (json['id'] as num).toInt(),
      email: json['email'] ?? "" as String,
      appleIdentifier: json['apple_identifier'] as String?,
      confirmedAt: json['confirmed_at'] as String?,
      favouriteBuyers: (json['favourite_buyers'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      hashedPassword: json['hashed_password'] as String?,
      insertedAt: json['inserted_at'] as String?,
      password: json['password'] as String?,
      profile: json['profile'] == null
          ? null
          : UserProfile.fromJson(json['profile'] as Map<String, dynamic>),
      updatedAt: json['updated_at'] as String?,
      token: json['token'] as String?,
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'apple_identifier': instance.appleIdentifier,
      'confirmed_at': instance.confirmedAt,
      'favourite_buyers': instance.favouriteBuyers,
      'hashed_password': instance.hashedPassword,
      'inserted_at': instance.insertedAt,
      'password': instance.password,
      'profile': instance.profile?.toJson(),
      'updated_at': instance.updatedAt,
      'token': instance.token,
    };

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      id: (json['id'] as num).toInt(),
      agentEmail: json['agent_email'] as String?,
      imageUrl: json['image_url'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      primaryPhoneNumber: json['phone_number_primary'] as String?,
      brokerageName: json['brokerage_name'] as String?,
      brokerageLisenceNo: json['brokerage_lisence_no'] as String?,
      brokerageStreetAddress: json['broker_street_address'] as String?,
      brokerageCity: json['broker_city'] as String?,
      brokerageZipCode: json['brokerage_zip_code'] as String?,
      brokerageState: json['brokerage_state'] as String?,
      isProfileCompleted: json['is_completed'] as bool? ?? false,
      agentLicenseIdNo: json['lisence_id_no'] as String?,
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'agent_email': instance.agentEmail,
      'image_url': instance.imageUrl,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'phone_number_primary': instance.primaryPhoneNumber,
      'brokerage_name': instance.brokerageName,
      'brokerage_lisence_no': instance.brokerageLisenceNo,
      'broker_street_address': instance.brokerageStreetAddress,
      'broker_city': instance.brokerageCity,
      'brokerage_zip_code': instance.brokerageZipCode,
      'brokerage_state': instance.brokerageState,
      'is_completed': instance.isProfileCompleted,
      'lisence_id_no': instance.agentLicenseIdNo,
    };
