defmodule BuyerboardBackendWeb.UserView do
  use BuyerboardBackendWeb, :view

  def render("user.json", %{user: user, message: message}) do
    user =
      user
      |> Map.from_struct()
      |> Map.drop([:__meta__, :buyers, :notes, :threads, :auth_providers])

    %{data: user, message: message}
  end

  def render("error.json", %{error: error}) do
    # Transform the error map to extract only the first message for each field
    formatted_error = Enum.into(error, %{}, fn {key, [message | _]} -> {key, message} end)
    %{error: formatted_error}
  end

  def render("message.json", %{message: message}) do
    %{message: message}
  end

  def render("error_account_deletion.json", %{message: message}) do
    %{message: message}
  end
end
