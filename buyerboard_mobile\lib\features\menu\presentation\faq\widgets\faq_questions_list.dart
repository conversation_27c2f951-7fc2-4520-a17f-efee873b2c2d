import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/menu/data/models/faq_model.dart';
import 'package:buyer_board/features/menu/presentation/faq/cubit/faq_screen_cubit.dart';
import 'package:buyer_board/features/menu/presentation/faq/state/faq_screen_state.dart';
import 'package:buyer_board/features/menu/presentation/faq/widgets/faq_question__expansion_tile_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FaqQuestionsList extends StatefulWidget {
  const FaqQuestionsList({super.key});

  @override
  State<FaqQuestionsList> createState() => _FaqQuestionsListState();
}

class _FaqQuestionsListState extends State<FaqQuestionsList> {
  @override
  void initState() {
    context.read<FaqScreenCubit>().getFaqs();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FaqScreenCubit, FaqScreenState>(
      builder: (context, state) {
        return state.maybeWhen(
          loading: () => const Center(
            child: CupertinoActivityIndicator(
              radius: 16,
              color: AppColors.primary,
            ),
          ),
          success: (faq) => _buildQuestionsList(faq.question),
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildQuestionsList(List<FaqQuestionData> questions) {
    return Expanded(
      child: ListView.builder(
        // separatorBuilder: (context, index) => const Divider(
        //   color: AppColors.greyLight,
        // ),
        itemCount: questions.length,
        itemBuilder: (context, index) => FaqQuestionExpansionTileWidget(
          question: questions[index],
        ),
      ),
    );
  }
}
