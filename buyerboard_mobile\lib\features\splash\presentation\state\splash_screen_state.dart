import 'package:freezed_annotation/freezed_annotation.dart';
part 'splash_screen_state.freezed.dart';

@freezed
class SplashScreenState with _$SplashScreenState {
  const factory SplashScreenState.initial() = initial;
  const factory SplashScreenState.success(String route) = success;
  const factory SplashScreenState.localAuth() = localAuth;
  const factory SplashScreenState.splashError(String? error) = splashError;
}
