import 'dart:io';

import 'package:buyer_board/core/network/base_response.dart';
import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/profile/data/models/requests/update_profile_request.dart';
import 'package:buyer_board/features/profile/data/models/response/upload_image_response.dart';
import 'package:buyer_board/features/profile/domain/repository/user_repository.dart';

import '../models/response/user_address_response.dart';

class UserRepositoryImpl extends UserRepository {
  UserRepositoryImpl({required this.restAPIClient});
  final RestAPIClient restAPIClient;
  @override
  Future<BaseResponse<UserProfile>> updateUserProfile(
      {required UpdateProfileRequest updateProfileRequest}) async {
    return await restAPIClient.updateUserProfile(
      updateProfileRequest: updateProfileRequest,
    );
  }

  @override
  Future<UploadImage?> uploadProfileImage(
      {required File profileImage}) async {
    final response = await restAPIClient.uploadImage(image: profileImage);
    return response.data;
  }

  @override
  Future<BaseResponse<UserAddress>> getAddressFromZipCode(
          {required String zipCode}) async =>
      await restAPIClient.getAddressFromZipCode(zipCode: zipCode);
}
