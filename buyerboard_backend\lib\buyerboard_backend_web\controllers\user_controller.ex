defmodule BuyerboardBackendWeb.UserController do
  use BuyerboardBackendWeb, :controller
  import Ecto.Query, warn: false
  alias BuyerboardBackend.{Repo, Profile, Message, Buyer, User}

  alias BuyerboardBackend.Accounts.AuthProviders
  alias BuyerboardBackend.Accounts.AuthProvider
  alias BuyerboardBackend.Accounts
  alias BuyerboardBackend.Accounts.User

  alias BuyerboardBackend.{Profile, Repo}

  def send_delete_link(conn, %{"email" => email}) do


    case Accounts.get_user_by_email(email) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render("error_account_deletion.json", %{message: "User not found"})

      %User{} = user ->
        # Generate the deletion link
        delete_link = generate_delete_link(user)

        # Send the delete link to the user's email
        case BuyerboardBackend.Accounts.UserNotifier.deliver_account_deleted_email_link(
               user,
               delete_link
             ) do
          # Handle the success case that returns an email struct
          {:ok, _email_struct} ->
            conn
            |> put_status(:ok)
            |> render("message.json", %{message: "Deletion link sent successfully"})

          # Handle error if email sending fails
          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> render("error.json", %{message: "Failed to send deletion link: #{reason}"})
        end
    end
  end

  # Add this attribute to suppress Dialyzer warnings for this function
  @dialyzer {:nowarn_function, {:delete_account, 2}}


  def delete_account(conn, %{"hash" => hash}) do
    # Search for user by hashed email
    user =
      Repo.one(
        from u in User,
        where: fragment("encode(digest(CAST(? AS text), 'sha256'), 'hex')", u.email) == ^hash
      )

    case user do
      nil ->
        # If the user is not found, return an error
        conn
        |> put_status(:bad_request)
        |> render("error.json", %{message: "Invalid or expired link"})

      %User{} ->
        case BuyerboardBackend.Accounts.deleteAccount(user.id) do
          # If at least one row is affected, delete is successful
          {:ok, {count, _}} when count > 0 ->
            conn
            |> put_status(:ok)
            |> render("message.json", %{message: "Account deleted successfully"})

          # If no rows are affected, the user is already deleted or not found
          {:ok, {0, _}} ->
            conn
            |> put_status(:bad_request)
            |> render("message.json", %{message: "Account not found or already deleted"})

          # If there’s an error, respond with internal server error
          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> render("error.json", %{message: "Failed to delete account: #{reason}"})
        end
    end
  end

  def show(%{assigns: %{current_user: nil}} = conn, _user_params) do
    conn
    |> put_status(:bad_request)
    |> render("error.json", error: %{message: "Unauthenticated"})
  end

  def show(conn, %{"id" => user_id}) do
    case Accounts.get_user(user_id) do
      %User{} = user ->
        message = %{title: nil, body: "Successfully fetched user"}

        render(conn, "user.json", %{user: user, message: message})

      _ ->
        conn
        |> put_status(:bad_request)
        |> render("error.json", error: "User not found")
    end
  end

  def social_sign_up(conn, user_params) do
    case Accounts.get_user_by_email(user_params["email"]) ||
           AuthProviders.check_user_if_exists?(user_params) do
      nil ->
        # Register the user
        {:ok, user} = Accounts.register_user_by_social(user_params)

        # Generate the token
        token = Accounts.generate_user_session_token(user) |> Base.url_encode64()
        user = Map.put(user, "token", token)

        # Insert or update profile with first and last name
        update_or_create_profile(user, user_params)

        # Fetch the user again with the preloaded profile to ensure it's loaded
        user_with_profile = Repo.get(User, user.id) |> Repo.preload(:profile)

        # Re-add the token to the user struct after fetching from DB
        user_with_profile = Map.put(user_with_profile, "token", token)

        message = %{title: nil, body: "Successfully signed up"}
        render(conn, "user.json", %{user: user_with_profile, message: message})

      %User{} = user ->
        # Generate the token
        token = Accounts.generate_user_session_token(user) |> Base.url_encode64()
        user = Map.put(user, "token", token)

        # Insert or update profile with first and last name
        #        update_or_create_profile(user, user_params)

        # Fetch the user again with the preloaded profile
        user_with_profile = Repo.get(User, user.id) |> Repo.preload(:profile)

        # Re-add the token to the user struct after fetching from DB
        user_with_profile = Map.put(user_with_profile, "token", token)

        message = %{title: nil, body: "Successfully signed up"}
        render(conn, "user.json", %{user: user_with_profile, message: message})
    end
  end

  def apple_sign_up(conn, user_params) do
    case Accounts.get_user_by_identifier(user_params["apple_identifier"]) ||
           AuthProviders.check_user_if_exists?(user_params) do
      nil ->
        {:ok, user} = Accounts.register_user_by_apple(user_params)
        token = Accounts.generate_user_session_token(user) |> Base.url_encode64()
        user = Map.put(user, "token", token)

        # Insert or update profile with first and last name if it doesn't exist
        update_or_create_profile(user, user_params)

        # Fetch the user again with the preloaded profile
        user_with_profile = Repo.get(User, user.id) |> Repo.preload(:profile)
        message = %{title: nil, body: "Successfully signed up"}
        render(conn, "user.json", %{user: user, message: message})

      %User{} = user ->
        token = Accounts.generate_user_session_token(user) |> Base.url_encode64()
        user = Map.put(user, "token", token)

        # Insert or update profile with first and last name if profile is nil
        #        update_or_create_profile(user, user_params)

        # Preload profile here before sending the response
        user = Repo.preload(user, :profile)
        message = %{title: nil, body: "Successfully signed up"}
        render(conn, "user.json", %{user: user, message: message})
    end
  end

  def create(conn, user_params) do
    case Accounts.register_user(user_params) do
      {:ok, user} ->
        token = Accounts.generate_user_session_token(user) |> Base.url_encode64()
        user = Map.put(user, "token", token)

        message = %{title: nil, body: "Successfully signed up"}

        render(conn, "user.json", %{user: user, message: message})

      {:error, %Ecto.Changeset{} = changeset} ->
        error = translate_errors(changeset)

        conn
        |> put_status(:bad_request)
        |> render("error.json", error: error)
    end
  end

  def update_or_create_profile(user, user_params) do
    profile_changeset =
      case Repo.get_by(Profile, user_id: user.id) do
        nil ->
          # If profile doesn't exist, create a new one with provided values
          %Profile{}
          |> Profile.changeset(%{
            user_id: user.id,
            first_name: Map.get(user_params, "first_name"),
            last_name: Map.get(user_params, "last_name"),
            agent_email: Map.get(user_params, "email")
          })

        profile ->
          # If profile exists, only update first_name and last_name if they are nil
          updated_params = %{
            first_name: profile.first_name || Map.get(user_params, "first_name"),
            last_name: profile.last_name || Map.get(user_params, "last_name")
          }

          profile
          |> Profile.changeset(updated_params)
      end

    Repo.insert_or_update(profile_changeset)
  end

  # def update_profile(%{assigns: %{current_user: nil}} = conn, _user_params) do
  #   conn
  #   |> put_status(:bad_request)
  #   |> render("error.json", error: %{message: "Unauthenticated"})
  # end

  # def update_profile(%{assigns: %{current_user: %{id: id}}} = conn, user_params) do
  #   with user <- Accounts.get_user(id),
  #        params <- Accounts.verify_email_change(user_params, user.email),
  #        {:ok, %User{} = user} <- Accounts.update_users_profile(user, params) do
  #     message = %{title: nil, body: "Successfully Updated profile"}

  #     render(conn, "user.json", %{user: user, message: message})
  #   else
  #     e ->
  #       case e do
  #         {:error, %Ecto.Changeset{} = changeset} ->
  #           error = translate_errors(changeset)

  #           conn
  #           |> put_status(:bad_request)
  #           |> render("error.json", error: error)

  #         true ->
  #           conn
  #           |> put_status(:bad_request)
  #           |> render("error.json", error: %{message: "User not found"})

  #         {:error, :unauthorized} ->
  #           conn
  #           |> put_status(:bad_request)
  #           |> render("error.json", error: %{message: "Unauthenticated"})
  #       end
  #   end
  # end

  defp translate_errors(user) do
    Ecto.Changeset.traverse_errors(user, &BuyerboardBackendWeb.ErrorHelpers.translate_error/1)
  end

  defp generate_delete_link(user) do
    hashed_email = hash_email(user.email)
    # Generate a link for the frontend or the API endpoint
    "https://bb.vdev.tech/api/delete/#{hashed_email}"
  end

  # Function to hash the user's email
  defp hash_email(email) do
    :crypto.hash(:sha256, email) |> Base.encode16(case: :lower)
  end

  defp send_delete_email(email, delete_link) do
    IO.puts("Sending delete link to #{email}: #{delete_link}")
    # Replace with actual email sending logic
    :ok
  end

  # Swagger Implementations
  swagger_path :create do
    post("/users/register")
    summary("Create User")

    description("Signup user")

    produces("application/json")
    security([%{Bearer: []}])

    parameters do
      body(:body, Schema.ref(:CreateUser), "Create New User by passing params", required: true)
    end

    response(200, "Ok", Schema.ref(:users))
  end

  swagger_path :social_sign_up do
    post("/social-sign-up")
    summary("Signs up user with social accounts (google/apple)")

    description("Signup user with social accounts")

    produces("application/json")
    security([%{Bearer: []}])

    parameters do
      body(
        :body,
        Schema.ref(:CreateUserSocially),
        "Create New User by passing params returned by Social Accounts",
        required: true
      )
    end

    response(200, "Ok", Schema.ref(:users))
  end

  swagger_path :apple_sign_up do
    post("/apple-sign-up")
    summary("Signs up user with apple account")

    description("Signup user with apple accounts")

    produces("application/json")
    security([%{Bearer: []}])

    parameters do
      body(
        :body,
        Schema.ref(:CreateUserByApple),
        "Create New User by passing params returned by Apple Accounts",
        required: true
      )
    end

    response(200, "Ok", Schema.ref(:users))
  end

  # swagger_path :update_profile do
  #   put("/users/profile")
  #   summary("Updates Profile")

  #   description("Updates logged in user's profile")

  #   produces("application/json")
  #   security([%{Bearer: []}])

  #   parameters do
  #     body(:body, Schema.ref(:UpdateProfile), "Create New User by passing params", required: true)
  #   end

  #   response(200, "Ok", Schema.ref(:users))
  # end

  swagger_path :show do
    get("/user/{id}")
    summary("Get user details")

    description("Get user details by passing user id")

    produces("application/json")
    security([%{Bearer: []}])

    parameters do
      id(:path, :integer, "User ID", required: true)
    end

    response(200, "Ok", Schema.ref(:users))
  end

  def swagger_definitions do
    %{
      CreateUser:
        swagger_schema do
          title("Create User")
          description("Signup user")

          properties do
            email(:string, "User email must have the @ sign and no spaces")
            password(:string, "Pasword must be min 12 and max 72")
          end

          example(%{
            email: "<EMAIL>",
            password: "password/123"
          })
        end,
      CreateUserSocially:
        swagger_schema do
          title("Create User with Social Accounts")
          description("Signup user by Social Accounts")

          properties do
            email(:string, "User email must have the @ sign and no spaces")
            first_name(:string, "User first name")
            last_name(:string, "User last name")
            provider(:string, "Social Provider (Google/Apple)")
          end

          example(%{
            email: "<EMAIL>",
            first_name: "User First Name",
            last_name: "User Last Name",
            provider: "google"
          })
        end,
      CreateUserByApple:
        swagger_schema do
          title("Create User with Social Accounts")
          description("Signup user by Social Accounts")

          properties do
            email(:string, "User email must have the @ sign and no spaces")
            apple_identifier(:string, "Apple identifier returned by apple auth")
            provider(:string, "Social Provider (Google/Apple)")
          end

          example(%{
            email: "<EMAIL>",
            apple_identifier: "apple_unique_identifier",
            provider: "google"
          })
        end,
      # UpdateProfile:
      #   swagger_schema do
      #     title("Updates user's profile")
      #     description("Updates user's profile")

      #     properties do
      #       first_name(:string, "User's first name")
      #       last_name(:string, "User's last name")
      #       email(:string, "User email must have the @ sign and no spaces")
      #       phone_number_primary(:string, "User's primary phone number")
      #       image_url(:string, "User's avatar url")
      #       brokerage_name(:string, "Broker's Name")
      #       brokerage_lisence_no(:string, "Broker's lisence number")
      #       lisence_id_no(:string, "Lisence ID number")
      #       broker_street_address(:string, "Broker's Street Address")
      #       broker_city(:string, "Broker's City")
      #       brokerage_zip_code(:string, "Broker's zip code")
      #       brokerage_state(:string, "Broker's State")
      #     end

      #     example(%{
      #       email: "<EMAIL>",
      #       first_name: "User's first Name",
      #       last_name: "User's last Name",
      #       phone_number_primary: "+012345789",
      #       image_url: "/uploads/9C96BFEA-4192-4396-AC69-41234EE55236_1_201_a.png",
      #       brokerage_name: "Broker Name",
      #       brokerage_lisence_no: "LIS1234",
      #       lisence_id_no: "REVS12345",
      #       broker_street_address: "123, Hope Street",
      #       broker_city: "New York",
      #       brokerage_zip_code: "12345",
      #       brokerage_state: "CA"
      #     })
      #   end,
      users:
        swagger_schema do
          properties do
            id(:integer, "User unique id")
            email(:string, "Email Value", required: true)
            password(:string, "Password Value", required: true)
            phone_number_primary(:string, "Primary phone number")
            first_name(:string, "First name of user")
            last_name(:string, "Last name of user")
            provider(:string, "password")
            image_url(:string, "Avatar url")
            hashed_password(:string, "Hashed Password")
            brokerage_name(:string, "Broker's Name")
            brokerage_lisence_no(:string, "Broker's lisence number")
            lisence_id_no(:string, "Lisence ID number")
            broker_street_address(:string, "Broker's Street Address")
            broker_city(:string, "Broker's City")
            brokerage_zip_code(:string, "Broker's zip code")
            brokerage_state(:string, "Broker's State")
            confirmed_at(:string, "User confirmed at Datetime")
            inserted_at(:string, "User inserted at Datetime")
            updated_at(:string, "User updated at Datetime")
          end

          example(%{
            data: %{
              confirmed_at: "2024-05-20T20:20:37Z",
              email: "<EMAIL>",
              hashed_password: "$2b$12$xzVN8p9own/R6BijKKqRXuOgWjXCrRcWavf6S6j5eZ3eXHHAt421u",
              id: 26,
              inserted_at: "2024-05-20T20:20:37Z",
              password: "2024-05-20T20:20:37Z",
              updated_at: "2024-05-20T20:20:37Z",
              token: "R7-vrW98kLb-CyAKHWeRo8im2wxOdOcTd_pwHAJc9uE=",
              phone_number_primary: "+123456",
              first_name: "First Name",
              last_name: "Last Name",
              provider: "password",
              image_url: "/avatar.png",
              brokerage_name: "Broker Name",
              brokerage_lisence_no: "LIS1234",
              lisence_id_no: "REVS12345",
              broker_street_address: "123, Hope Street",
              broker_city: "New York",
              brokerage_zip_code: "12345",
              brokerage_state: "CA"
            },
            message: %{
              body: "Successfully signed up",
              title: "null"
            }
          })
        end
    }
  end
end
