import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/address_from_zip_cubit.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/buyer_info_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/auth_navigation_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/login_with_email_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/logout_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/register_with_email_password_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/social_auth_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filter_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filtered_value_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_search_slide_animation_type_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/device_current_location_cubit.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chats_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_notification_cubit.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_socket_connection_cubit.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chats_search_cubit.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/note_favorite_cubit.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_cubit.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_selection_cubit.dart';
import 'package:buyer_board/features/forget_password/presentation/cubit/reset_passsord_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_cubit.dart';
import 'package:buyer_board/features/home/<USER>/cubit/chat_tab_badge_cubit.dart';
import 'package:buyer_board/features/home/<USER>/cubit/home_tab_cubit.dart';
import 'package:buyer_board/features/intro/presentation/bloc/intro_hydrated_cubit.dart';
import 'package:buyer_board/features/menu/cubit/intro_to_buyer_board_cubit.dart';
import 'package:buyer_board/features/menu/presentation/faq/cubit/faq_screen_cubit.dart';
import 'package:buyer_board/features/menu/presentation/help/cubit/help_screen_cubit.dart';
import 'package:buyer_board/features/onboarding/presentation/cubit/onboarding_cubit.dart';
import 'package:buyer_board/features/profile/presentation/cubit/address_cubit.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:buyer_board/features/settings/presentation/cubit/delete_account_cubit.dart';
import 'package:buyer_board/features/settings/presentation/cubit/notifications_preferences_cubit.dart';
import 'package:buyer_board/features/settings/presentation/cubit/share_location_cubit.dart';
import 'package:buyer_board/features/settings/presentation/cubit/theme_cubit.dart';
import 'package:buyer_board/features/splash/presentation/cubit/splash_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../features/add_buyer/presentation/cubit/add_buyer_cubit.dart';
import '../di/injector.dart';
import 'app_router.dart';

class BuyerBoardApp extends StatefulWidget {
  const BuyerBoardApp({super.key});

  static BuildContext? get appContext =>
      AppRouter.router.routerDelegate.navigatorKey.currentContext;

  @override
  State<StatefulWidget> createState() => _BuyerBoardAppState();
}

class _BuyerBoardAppState extends State<BuyerBoardApp> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: appProviders,
      child: BlocBuilder<ThemeCubit, ThemeMode>(
        builder: (_, state) {
          return MaterialApp.router(
            title: Strings.appName,
            theme: AppTheme.light,
            darkTheme: AppTheme.dark,
            themeMode: state,
            debugShowCheckedModeBanner: false,
            routeInformationParser: AppRouter.router.routeInformationParser,
            routeInformationProvider: AppRouter.router.routeInformationProvider,
            routerDelegate: AppRouter.router.routerDelegate,
            builder: EasyLoading.init(),
          );
        },
      ),
    );
  }

  get appProviders => [
        BlocProvider<UserSessionCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<SplashCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<SocialAuthCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<LoginWithEmailCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<SignUpWithEmailCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<OnBoardingCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<ResetPasswordCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<ProfileCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<AddBuyerCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<BuyerInfoCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<BuyersBloc>(lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<FavouriteBuyersCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<IntroToBuyerBoardCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<HelpScreenCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<FaqScreenCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<NoteFavoriteCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<LogoutCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<ThemeCubit>(
          create: (_) => ThemeCubit(),
        ),
        BlocProvider<ShareLocationCubit>(
          create: (_) => ShareLocationCubit(),
        ),
        BlocProvider(
          create: (_) => NotificationsPreferencesCubit(),
        ),
        BlocProvider<AddressCubit>(
            lazy: true, create: (_) => Injector.resolve()),
        BlocProvider<UserAuthRouteCubit>(
          create: (_) => UserAuthRouteCubit(),
        ),
        BlocProvider<BuyerFilterLocationsCubit>(
          lazy: true,
          create: (_) => Injector.resolve(),
        ),
        BlocProvider(create: (_) => HomeBottomNarBarTabCubit()),
        BlocProvider(create: (_) => BuyerSearchSlideAnimationTypeCubit()),
        BlocProvider(create: (_) => CurrentLocationCubit()),
        BlocProvider(
          create: (_) => DeviceCurrentLocationCubit(
            repository: Injector.resolve(),
          ),
        ),
        BlocProvider(
          create: (_) => BuyerCubit(
            buyerRepository: Injector.resolve(),
          ),
        ),
        BlocProvider(
          create: (_) => ChatNotificationCubit(
            repository: Injector.resolve(),
          ),
        ),
        BlocProvider(
          create: (_) => ChatBloc(
            chatRepository: Injector.resolve(),
          ),
        ),
        BlocProvider(
          create: (_) => ChatSocketConnectionCubit(
            repository: Injector.resolve(),
          ),
        ),
        BlocProvider(
          create: (_) => AllChatsBloc(
            chatRepository: Injector.resolve(),
          ),
        ),
        BlocProvider(create: (_) => ChatsSearchCubit()),
        BlocProvider(
          create: (_) => AllArchiveChatsBloc(
            chatRepository: Injector.resolve(),
          )..add(GetAllArchiveChats()),
        ),
        BlocProvider(
          create: (_) => AddressFromZipCubit(
            userRepository: Injector.resolve(),
          ),
        ),
        BlocProvider(
          create: (_) => DeleteAccountCubit(Injector.resolve()),
        ),
        BlocProvider(create: (_) => ChatTabBadgeCubit()),
        BlocProvider(create: (_) => IntroHydratedCubit()),
        BlocProvider(create: (_) => NewFilterCubit()),
        BlocProvider(create: (_) => NewFilterSelectionCubit()),
      ];
}
