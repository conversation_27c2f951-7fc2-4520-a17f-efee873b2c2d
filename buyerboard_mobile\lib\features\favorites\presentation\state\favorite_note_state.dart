import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';

sealed class NoteFavoriteState {}

final class NoteFavoriteInitial extends NoteFavoriteState {}

final class NoteFavoriteLoading extends NoteFavoriteState {}

final class NoteFavoriteSuccess extends NoteFavoriteState {
  final BuyerModel buyerModel;
  NoteFavoriteSuccess(this.buyerModel);
}

final class NoteFavoriteError extends NoteFavoriteState {
  final String error;
  NoteFavoriteError(this.error);
}
