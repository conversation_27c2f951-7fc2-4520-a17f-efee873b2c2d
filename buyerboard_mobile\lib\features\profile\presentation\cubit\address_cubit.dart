import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/profile/domain/repository/user_repository.dart';
import 'package:buyer_board/features/profile/presentation/states/address_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddressCubit extends Cubit<AddressState> {
  final UserRepository userRepository;
  AddressCubit({required this.userRepository})
      : super(const AddressState.initial());

  void getAddressFromZipCode({required String zipCode}) async {
    emit(const AddressState.loading());
    try {
      final response =
          await userRepository.getAddressFromZipCode(zipCode: zipCode);
      if (response.data != null) {
        emit(AddressState.success(address: response.data!));
      } else {
        emit(const AddressState.addressError(Strings.commonError));
      }
    } catch (e) {
      emit(
        AddressState.addressError(e.toString()),
      );
    }
  }
}
