import 'package:hydrated_bloc/hydrated_bloc.dart';

class NotificationsPreferencesCubit extends HydratedCubit<
    ({
      bool incomingMessagesAgents,
      bool incomingMessagesBuyerBoard,
    })> {
  NotificationsPreferencesCubit()
      : super(
          (incomingMessagesAgents: true, incomingMessagesBuyerBoard: true),
        );

  void updateIncomingMessagesAgents(bool value) {
    emit(
      (
        incomingMessagesAgents: value,
        incomingMessagesBuyerBoard: state.incomingMessagesBuyerBoard
      ),
    );
  }

  void updateIncomingMessagesBuyerBoard(bool value) {
    emit(
      (
        incomingMessagesAgents: state.incomingMessagesAgents,
        incomingMessagesBuyerBoard: value
      ),
    );
  }

  @override
  Map<String, dynamic>? toJson(
      ({bool incomingMessagesAgents, bool incomingMessagesBuyerBoard}) state) {
    return {
      'incomingMessagesAgents': state.incomingMessagesAgents,
      'incomingMessagesBuyerBoard': state.incomingMessagesBuyerBoard,
    };
  }

  @override
  ({bool incomingMessagesAgents, bool incomingMessagesBuyerBoard})? fromJson(
      Map<String, dynamic> json) {
    return (
      incomingMessagesAgents: json['incomingMessagesAgents'] as bool,
      incomingMessagesBuyerBoard: json['incomingMessagesBuyerBoard'] as bool
    );
  }
}
