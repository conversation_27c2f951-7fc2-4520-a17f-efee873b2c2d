import 'package:buyer_board/core/resources/colors.dart';
import 'package:flutter/material.dart';

class OnBoardingPageIndicators extends StatelessWidget {
  const OnBoardingPageIndicators({
    super.key,
    required this.selectedIndex,
    this.count = 4,
    this.indicatorColor
  });
  final int selectedIndex;
  final int count;
  final Color? indicatorColor;

  List<Widget> get getPageIndicators => List.generate(
        count,
        (index) => PageIndicator(
          selected: index == selectedIndex,
          indicatorColor: indicatorColor,
        ),
      );

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: getPageIndicators,
    );
  }
}

class PageIndicator extends StatelessWidget {
  const PageIndicator({super.key, this.selected = true, this.size = 8, this.indicatorColor});
  final bool selected;
  final double size;
  final Color? indicatorColor;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: indicatorColor?.withOpacity(selected ? 1 : 0.3) ?? AppColors.white.withOpacity(selected ? 1 : 0.3),
        ),
        child: SizedBox(
          height: size,
          width: size,
        ),
      ),
    );
  }
}
