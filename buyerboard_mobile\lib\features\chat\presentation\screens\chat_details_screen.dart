// ignore_for_file: use_build_context_synchronously
import 'dart:async';
import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/extensions/string_extension.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/core/theme/app_typography.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_states.dart';
import 'package:buyer_board/features/chat/presentation/widgets/chat_item_widget.dart';
import 'package:buyer_board/features/chat/presentation/widgets/group_chat_item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/resources/strings.dart';
import '../widgets/chat_buyer_tile.dart';

class ChatDetailsScreen extends StatefulWidget {
  const ChatDetailsScreen({
    super.key,
    required this.buyer,
    required this.agent,
  });
  final ChatGroupModel buyer;
  final ChatGroupThreadModel agent;

  @override
  State<ChatDetailsScreen> createState() => _ChatDetailsScreenState();
}

class _ChatDetailsScreenState extends State<ChatDetailsScreen> {
  late ChatBloc _chatBloc;
  final appPreferences = Injector.resolve<AppPreferences>();
  final userSessionCubit = Injector.resolve<UserSessionCubit>();

  bool _isDeleteMode = false;
  final _selectedDeletionIds = <int>{};

  final _messageController = TextEditingController();
  final _focusNode = FocusNode();
  late StreamController<ChatMessage?> _streamReplyController;
  late StreamController<ChatMessage?> _streamEditController;
  @override
  void initState() {
    final userId = appPreferences.getUser()?.id ?? userSessionCubit.state?.id;
    if (userId != null) {
      context.read<ChatBloc>().add(
            GetChat(
              GetChatPayload(
                userId: userId,
                otherUserId: widget.agent.agentId,
                buyerId: widget.buyer.buyerId,
              ),
            ),
          );
      Future.microtask(() {
        if (widget.agent.id != null) {
          context.read<AllChatsBloc>().add(SeenChatEvent(
                buyerId: widget.buyer.buyerId,
                threadId: widget.agent.id!,
              ));
        }
      });
    }
    super.initState();
    _streamReplyController = StreamController<ChatMessage?>();
    _streamEditController = StreamController<ChatMessage?>();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _chatBloc.add(const CloseChatConnection());
    super.dispose();
  }

  void _handleSelection(int id) {
    setState(() {
      if (_selectedDeletionIds.contains(id)) {
        _selectedDeletionIds.remove(id);
      } else {
        _selectedDeletionIds.add(id);
      }
    });
  }

  void _onMessagesDelete({int? id}) async {
    final confirmed = await CoreUtils.showConfirmationDialog(
      context,
      title: _selectedDeletionIds.length > 1
          ? 'Delete Messages'
          : 'Delete Message',
      content: _selectedDeletionIds.length > 1
          ? 'Are you sure you want to delete these messages?'
          : 'Are you sure you want to delete this message?',
      action: 'Delete',
    );
    if (confirmed && mounted) {
      final payload = DeleteMessagesPayload(
        userId: appPreferences.getUser()?.id ?? -1,
        otherUserId: widget.agent.agentId,
        buyerId: widget.buyer.buyerId,
        messageIds: id != null ? {id} : _selectedDeletionIds,
      );
      context.read<ChatBloc>().add(DeleteMessages(payload));
    }
  }

  @override
  Widget build(BuildContext context) {
    _chatBloc = context.read<ChatBloc>();
    final typography = context.typography;
    final xColors = context.theme.appColors;
    final brokerFName = widget.agent.user?.profile?.firstName ?? '';
    final brokerLName = widget.agent.user?.profile?.lastName ?? '';
    final brokerFullName = ('$brokerFName $brokerLName').trim();

    return Scaffold(
      appBar: AppBar(
        title: _BrokerTitle(
          title: brokerFullName,
          subTitle: widget.agent.user?.profile?.brokerageName ?? '',
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedDeletionIds.clear();
                _isDeleteMode = !_isDeleteMode;
              });
            },
            child: Text(
              _isDeleteMode ? 'Cancel' : 'Delete',
              style: typography.largeReg.copyWith(
                color: xColors.whitePXLight,
              ),
            ),
          ),
          if (_isDeleteMode && _selectedDeletionIds.isNotEmpty)
            IconButton(
              onPressed: () => _onMessagesDelete(),
              icon: Icon(
                Icons.delete,
                color: context.theme.appColors.error,
              ),
            ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(65),
          child: BuyerTileWidget(
            buyerModel: widget.buyer,
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: _ConversationList(
              selectedDeletionIds: _selectedDeletionIds,
              isDeleteMode: _isDeleteMode,
              thread: widget.agent,
              userId: widget.agent.userId,
              agentId: widget.agent.agentId,
              buyerId: widget.buyer.buyerId,
              userAvatar: widget.agent.userImageUrl,
              agentAvatar: widget.agent.agentImageUrl,
              onSelectedForDeletion: (selectedId) {
                _onMessagesDelete(id: selectedId);
              },
              onMultipleDeletionSelection: _handleSelection,
              streamController: _streamReplyController,
              editStreamController: _streamEditController,
              onEditTap: (String message) {
                _messageController.text = message;
                _focusNode.requestFocus();
              },
              onReplyTap: () => _focusNode.requestFocus(),
            ),
          ),
          _SendMessageSection(
            userId: widget.agent.userId,
            otherUserId: widget.agent.agentId,
            buyerId: widget.buyer.buyerId,
            messageController: _messageController,
            focusNode: _focusNode,
            stream: _streamReplyController,
            editStream: _streamEditController,
            onCancelReply: () {
              _streamReplyController.add(null);
              _streamEditController.add(null);
            },
          ),
        ],
      ),
    );
  }
}

class _BrokerTitle extends StatelessWidget {
  const _BrokerTitle({
    required this.title,
    required this.subTitle,
  });

  final String title;
  final String subTitle;

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final xColors = context.theme.appColors;
    return Column(
      children: [
        Text(title,
            style: typography.largeSemi.copyWith(
              color: xColors.whitePXLight,
            )),
        Text(
          subTitle,
          style: typography.small1xReg.copyWith(
            color: xColors.pLightPMedium,
          ),
        ),
      ],
    );
  }
}

class _SendMessageSection extends StatefulWidget {
  const _SendMessageSection({
    required this.userId,
    required this.otherUserId,
    required this.buyerId,
    required this.focusNode,
    required this.stream,
    required this.editStream,
    required this.onCancelReply,
    required this.messageController,
  });

  final int userId;
  final int otherUserId;
  final int buyerId;
  final TextEditingController messageController;
  final FocusNode focusNode;
  final StreamController stream;
  final StreamController editStream;
  final VoidCallback onCancelReply;

  @override
  State<_SendMessageSection> createState() => _SendMessageSectionState();
}

class _SendMessageSectionState extends State<_SendMessageSection> {
  int? _parentMessageId;
  int? _editMessageId;
  bool _isMessageEmpty = true;

  @override
  void initState() {
    super.initState();

    widget.messageController.addListener(() {
      setState(() {
        _isMessageEmpty = widget.messageController.text.trim().isEmpty;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 8,
        bottom: 12 + MediaQuery.paddingOf(context).bottom,
      ),
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          StreamBuilder(
            stream: widget.editStream.stream,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data != null) {
                final chatMessage = snapshot.data as ChatMessage;
                if (_parentMessageId == null) {
                  _editMessageId = chatMessage.id;
                  return _buildEditView(colors, chatMessage);
                }
              }
              return const SizedBox();
            },
          ),
          StreamBuilder(
            stream: widget.stream.stream,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data != null) {
                final chatMessage = snapshot.data as ChatMessage;
                if (_editMessageId == null) {
                  _parentMessageId = chatMessage.id;
                  return _buildReplyView(colors, chatMessage);
                }
              }
              return const SizedBox();
            },
          ),
          Row(
            children: [
              IconButton(
                onPressed: () async {
                  final result =
                      await context.read<ChatBloc>().selectAttachments();
                  final List selectedImages = result['valid'] ?? [];
                  final bool hasInvalidFiles =
                      result['hasInvalidFiles'] ?? false;
                  final error = result['error'];
                  if (error != null) {
                    Fluttertoast.showToast(
                        msg: "$error",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  }
                  if (hasInvalidFiles && selectedImages.isEmpty) {
                    Fluttertoast.showToast(
                        msg:
                            "Selected files are larger than 500MB and were skipped.",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  }
                  if (hasInvalidFiles && selectedImages.isNotEmpty) {
                    Fluttertoast.showToast(
                        msg:
                            "Some of the selected files are larger than 500MB and were skipped.",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  }
                  if (selectedImages.isNotEmpty) {
                    await Future.delayed(const Duration(milliseconds: 500));
                    context.pushNamed(
                      PagePath.attachmentDetailsScreen,
                      extra: {
                        'imagePaths': selectedImages,
                        'userId': widget.userId,
                        'otherUserId': widget.otherUserId,
                        'buyerId': widget.buyerId,
                        'stream': widget.stream.stream,
                        'editStream': widget.editStream.stream,
                        'onCancelReply': widget.onCancelReply,
                        'parentMessageId': _parentMessageId,
                        'editMessageId': _editMessageId,
                        'isMessageEmpty': _isMessageEmpty,
                      },
                    );
                  }
                },
                style: IconButton.styleFrom(
                  backgroundColor: colors.surfaceContainerHigh,
                  minimumSize: const Size(34, 34),
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                icon: Icon(Icons.add, color: colors.onSurface),
              ),
              spacerW12,
              Expanded(
                child: SizedBox(
                  height: 39,
                  child: TextField(
                    focusNode: widget.focusNode,
                    controller: widget.messageController,
                    onSubmitted: (value) {
                      if (value.trim().isNotEmpty) {
                        if (_parentMessageId != null) {
                          final payload = SendReplyMessagePayload(
                            buyerId: widget.buyerId,
                            message: value.trim(),
                            userId: widget.userId,
                            otherUserId: widget.otherUserId,
                            parentId: _parentMessageId!,
                          );
                          context.read<ChatBloc>().add(SendMessage(payload));
                          _parentMessageId = null;
                          widget.onCancelReply();
                        } else if (_editMessageId != null) {
                          final payload = EditMessagePayload(
                            userId: widget.userId,
                            otherUserId: widget.otherUserId,
                            buyerId: widget.buyerId,
                            messageId: _editMessageId!,
                            message: value.trim(),
                          );
                          context.read<ChatBloc>().add(SendMessage(payload));
                          _editMessageId = null;
                          widget.onCancelReply();
                        } else {
                          final offset = DateTime.now().timeZoneOffset;
                          final offsetInHours = offset.inHours;
                          final payload = SendMessagePayload(
                              buyerId: widget.buyerId,
                              message: value.trim(),
                              userId: widget.userId,
                              otherUserId: widget.otherUserId,
                              offset: offsetInHours);
                          context.read<ChatBloc>().add(SendMessage(payload));
                        }
                      }
                      widget.messageController.clear();
                    },
                    textInputAction: TextInputAction.send,
                    textCapitalization: TextCapitalization.sentences,
                    decoration: InputDecoration(
                      counterText: '',
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 0,
                      ),
                      isDense: true,
                      hintText: 'iMessage',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(100),
                        borderSide: BorderSide(
                          color: colors.outline,
                        ),
                      ),
                      suffixIcon: IconButton(
                        onPressed: () {
                          if (_isMessageEmpty) {
                          } else {
                            if (_parentMessageId != null) {
                              final payload = SendReplyMessagePayload(
                                buyerId: widget.buyerId,
                                message: widget.messageController.text.trim(),
                                userId: widget.userId,
                                otherUserId: widget.otherUserId,
                                parentId: _parentMessageId!,
                              );
                              context
                                  .read<ChatBloc>()
                                  .add(SendMessage(payload));
                              _parentMessageId = null;
                              widget.onCancelReply();
                            } else if (_editMessageId != null) {
                              final payload = EditMessagePayload(
                                userId: widget.userId,
                                otherUserId: widget.otherUserId,
                                buyerId: widget.buyerId,
                                messageId: _editMessageId!,
                                message: widget.messageController.text.trim(),
                              );
                              context
                                  .read<ChatBloc>()
                                  .add(SendMessage(payload));
                              _editMessageId = null;
                              widget.onCancelReply();
                            } else {
                              final offset = DateTime.now().timeZoneOffset;
                              final offsetInHours = offset.inHours;
                              final payload = SendMessagePayload(
                                buyerId: widget.buyerId,
                                message: widget.messageController.text.trim(),
                                userId: widget.userId,
                                otherUserId: widget.otherUserId,
                                offset: offsetInHours,
                              );
                              context
                                  .read<ChatBloc>()
                                  .add(SendMessage(payload));
                            }
                          }
                          widget.messageController.clear();
                        },
                        icon: Icon(
                          Icons.send,
                          size: 20,
                          color: _isMessageEmpty
                              ? colors.onSurface
                              : colors.primary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditView(ColorScheme colors, ChatMessage chatMessage) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  chatMessage.message,
                  maxLines: 2,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _editMessageId = null;
                widget.onCancelReply();
              });
            },
            icon: Icon(Icons.close_rounded, color: colors.onSurface),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyView(ColorScheme colors, ChatMessage chatMessage) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  chatMessage.message.isNotEmpty
                      ? chatMessage.message
                      : chatMessage.attachments.isNotEmpty
                          ? (chatMessage.attachments.any(
                                  (attachment) => attachment.type == 'video')
                              ? 'Video'
                              : 'Photo')
                          : '',
                  maxLines: 2,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _parentMessageId = null;
                widget.onCancelReply();
              });
            },
            icon: Icon(Icons.close_rounded, color: colors.onSurface),
          ),
        ],
      ),
    );
  }
}

final GlobalKey _key = GlobalKey();

class _ConversationList extends StatefulWidget {
  const _ConversationList({
    required this.userId,
    required this.agentId,
    required this.buyerId,
    this.userAvatar,
    this.agentAvatar,
    this.isDeleteMode = false,
    required this.selectedDeletionIds,
    this.onSelectedForDeletion,
    this.onMultipleDeletionSelection,
    required this.thread,
    required this.streamController,
    required this.editStreamController,
    this.onEditTap,
    this.onReplyTap,
  });
  final int userId;
  final int? agentId;
  final int? buyerId;
  final String? userAvatar;
  final String? agentAvatar;
  final bool isDeleteMode;
  final Set<int> selectedDeletionIds;
  final void Function(int)? onSelectedForDeletion;
  final void Function(int)? onMultipleDeletionSelection;

  final ChatGroupThreadModel thread;
  final StreamController<ChatMessage?> streamController;
  final StreamController<ChatMessage?> editStreamController;
  final VoidCallback? onReplyTap;
  final void Function(String)? onEditTap;

  @override
  State<_ConversationList> createState() => _ConversationListState();
}

class _ConversationListState extends State<_ConversationList> {
  final _preferences = Injector.resolve<AppPreferences>();
  final _userSessionCubit = Injector.resolve<UserSessionCubit>();
  void _copyToClipboard(BuildContext context, String textToCopy) {
    Clipboard.setData(ClipboardData(text: textToCopy)).then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Strings.messageCopied,
            style: AppTypography.styles(AppColors.white).largeSemi.copyWith(
                  color: AppColors.white,
                ),
          ),
        ),
      );
    });
  }

  void _showMenu(BuildContext context, Offset offset,
      ChatMessage currentMessage, userId, agentId, buyerId, isSender) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    showMenu(
      context: context,
      position: RelativeRect.fromRect(
        offset & const Size(40, 40), // Position the menu slightly offset
        Offset.zero & overlay.size,
      ),
      items: isSender
          ? [
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.copyChat,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.copyChat.toCapitalize(),
                            style: AppTypography.styles(
                                    context.theme.appColors.blackWhite)
                                .mediumReg
                                .copyWith(
                                  color: context.theme.appColors.blackWhite,
                                ),
                          ),
                          Icon(Icons.copy,
                              color: context.theme.appColors.blackWhite),
                        ],
                      ),
                    ),
                    Divider(
                        color:
                            context.theme.appColors.blackWhite.withOpacity(0.4))
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.editChat,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.editChat.toCapitalize(),
                            style: AppTypography.styles(
                                    context.theme.appColors.blackWhite)
                                .mediumReg
                                .copyWith(
                                  color: context.theme.appColors.blackWhite,
                                ),
                          ),
                          Icon(Icons.edit,
                              color: context.theme.appColors.blackWhite),
                        ],
                      ),
                    ),
                    Divider(
                        color:
                            context.theme.appColors.blackWhite.withOpacity(0.4))
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.replyChat,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.replyChat.toCapitalize(),
                            style: AppTypography.styles(
                                    context.theme.appColors.blackWhite)
                                .mediumReg
                                .copyWith(
                                  color: context.theme.appColors.blackWhite,
                                ),
                          ),
                          Icon(Icons.reply_outlined,
                              color: context.theme.appColors.blackWhite),
                        ],
                      ),
                    ),
                    Divider(
                        color:
                            context.theme.appColors.blackWhite.withOpacity(0.4))
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.deleteChat,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        Strings.deleteChat.toCapitalize(),
                        style: AppTypography.styles(
                                context.theme.appColors.blackWhite)
                            .mediumReg
                            .copyWith(
                              color: context.theme.appColors.blackWhite,
                            ),
                      ),
                      Icon(Icons.delete,
                          color: context.theme.appColors.blackWhite),
                    ],
                  ),
                ),
              ),
            ]
          : [
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.copyChat,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.copyChat.toCapitalize(),
                            style: AppTypography.styles(
                                    context.theme.appColors.blackWhite)
                                .mediumReg
                                .copyWith(
                                  color: context.theme.appColors.blackWhite,
                                ),
                          ),
                          Icon(Icons.file_copy_outlined,
                              color: context.theme.appColors.blackWhite),
                        ],
                      ),
                    ),
                    Divider(
                      color:
                          context.theme.appColors.blackWhite.withOpacity(0.4),
                    )
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.replyChat,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        Strings.replyChat.toCapitalize(),
                        style: AppTypography.styles(
                                context.theme.appColors.blackWhite)
                            .mediumReg
                            .copyWith(
                              color: context.theme.appColors.blackWhite,
                            ),
                      ),
                      Icon(Icons.reply_outlined,
                          color: context.theme.appColors.blackWhite),
                    ],
                  ),
                ),
              ),
            ],
    ).then((value) {
      if (value == Strings.copyChat) {
        _copyToClipboard(context, currentMessage.message);
      } else if (value == Strings.deleteChat) {
        if (currentMessage.id != null) {
          widget.onSelectedForDeletion?.call(currentMessage.id!);
        }
      } else if (value == Strings.replyChat) {
        widget.streamController.add(currentMessage);
        widget.onReplyTap?.call();
      } else if (value == Strings.editChat) {
        widget.editStreamController.add(currentMessage);
        widget.onEditTap?.call(currentMessage.message);
      }
    });
  }

  void _onLongPress(BuildContext context, LongPressStartDetails details,
      ChatMessage currentMessage, userId, agentId, buyerId, isSender) {
    _showMenu(context, details.globalPosition, currentMessage, userId, agentId,
        buyerId, isSender);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatBloc, ChatState>(builder: (context, state) {
      return switch (state) {
        ChatLoadingState() || ChatInitialState() => Center(
              child: CupertinoActivityIndicator(
            color: context.theme.appColors.pPXLight,
            radius: 15,
          )),
        ChatDataState(messages: List<ChatMessage> messages) => messages.isEmpty
            ? const Center(child: Text('No messages yet'))
            : _ChatGroupedList(
                messages: messages,
                thread: widget.thread,
                userId: widget.userId,
                agentId: widget.agentId ?? -1,
                buyerId: widget.buyerId ?? -1,
                userAvatar: widget.userAvatar ?? "",
                agentAvatar: widget.agentAvatar ?? "",
                isDeleteMode: widget.isDeleteMode,
                selectedDeletionIds: widget.selectedDeletionIds,
                onSelectedForDeletion: widget.onSelectedForDeletion,
                onMultipleDeletionSelection: widget.onMultipleDeletionSelection,

                streamController: widget.streamController,
                editStreamController: widget.editStreamController,
                onEditTap: widget.onEditTap,
                onReplyTap: widget.onReplyTap,
              ),
        ChatErrorState() => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.wifi_off,
                  color: Color.fromARGB(255, 14, 106, 211),
                  size: 48,
                ),
                const SizedBox(height: 10),
                SizedBox(
                  width: 150,
                  child: CommonButton.basic(
                    label: Strings.reconnect,
                    action: () {
                      final userId = _preferences.getUser()?.id ??
                          _userSessionCubit.state?.id;
                      if (userId != null) {
                        context
                            .read<ChatBloc>()
                            .add(const CloseChatConnection());
                        context
                            .read<AllChatsBloc>()
                            .add(const CloseAllChatConnections());

                        context.read<AllChatsBloc>().add(GetAllChats());
                        context.read<ChatBloc>().add(
                              GetChat(
                                GetChatPayload(
                                  userId: userId,
                                  otherUserId: widget.agentId ?? -1,
                                  buyerId: widget.buyerId ?? -1,
                                ),
                              ),
                            );
                        Future.microtask(() {
                          if (widget.agentId != null) {
                            context.read<AllChatsBloc>().add(SeenChatEvent(
                                  buyerId: widget.buyerId ?? -1,
                                  threadId: widget.agentId ?? -1,
                                ));
                          }
                        });
                      }
                    },
                    backgroundColor: context.theme.appColors.pPXLight,
                    textColor: context.theme.appColors.whitePXDark,
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  "Socket disconnected due to unstable Internet.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.grey,
                    fontSize: 15,
                  ),
                ),
              ],
            ),
          ),
      };
    });
  }
}

class _ChatGroupedList extends StatelessWidget {
  _ChatGroupedList({
    required this.messages,
    required this.thread,
    required this.userId,
    required this.agentId,
    required this.buyerId,
    this.userAvatar,
    this.agentAvatar,
    this.isDeleteMode = false,
    required this.selectedDeletionIds,
    required this.onSelectedForDeletion,
    required this.onMultipleDeletionSelection,
    required this.streamController,
    required this.editStreamController,
    this.onEditTap,
    this.onReplyTap,
  }) : _preferences = Injector.resolve<AppPreferences>();

  final List<ChatMessage> messages;
  final AppPreferences _preferences;
  final int userId;
  final int agentId;
  final int buyerId;
  final String? userAvatar;
  final String? agentAvatar;
  final bool isDeleteMode;
  final Set<int> selectedDeletionIds;
  final void Function(int)? onSelectedForDeletion;
  final void Function(int)? onMultipleDeletionSelection;
  final StreamController<ChatMessage?> streamController;
  final StreamController<ChatMessage?> editStreamController;
  final void Function(String)? onEditTap;
  final VoidCallback? onReplyTap;
  final ChatGroupThreadModel thread;

  @override
  Widget build(BuildContext context) {
    final groupedMessages = groupMessagesByDateAndAttachments(messages);
    final firstName = thread.user?.profile?.firstName!;
    final lastName = thread.user?.profile?.lastName!;
    final myFirstName = _preferences.getUser()?.profile?.firstName!;
    final myLastName = _preferences.getUser()?.profile?.lastName!;
    final myNameFirstLetters = (myFirstName?[0] ?? '') + (myLastName?[0] ?? '');
    final otherUserNamefirstLetters =
        (firstName?[0] ?? '') + (lastName?[0] ?? '');
    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      removeBottom: true,
      child: ListView.builder(
        reverse: true,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: groupedMessages.length,
        itemBuilder: (context, index) {
          final group = groupedMessages.reversed.toList()[index];
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Text(
                    group.label,
                    style: context.typography.mediumSemi.copyWith(
                      color: context.appColors.greyM,
                    ),
                  ),
                ),
              ),
              ListView.separated(
                reverse: true,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: group.messages.length,
                itemBuilder: (context, messageIndex) {
                  final reversedMessages = group.messages.reversed.toList();
                  final currentMsg = reversedMessages[messageIndex];
                  final isSender = currentMsg is ChatMessage
                      ? currentMsg.sentBy == userId
                      : currentMsg is List<ChatMessage>
                          ? currentMsg.first.sentBy == userId
                          : false;
                  bool showAvatar = false;
                  if (messageIndex == 0) {
                    showAvatar = true;
                  } else {
                    final prevMsg = reversedMessages[messageIndex - 1];
                    if (currentMsg is ChatMessage && prevMsg is ChatMessage) {
                      if (currentMsg.sentBy != prevMsg.sentBy) {
                        showAvatar = true;
                      } else {
                        if (currentMsg.sentBy !=
                            group.messages.reversed
                                .toList()[messageIndex - 1]
                                .sentBy) {
                          showAvatar = true;
                        }
                      }
                    }
                  }
                  if (reversedMessages[messageIndex] is List) {
                    final dynamicList = reversedMessages[messageIndex] as List;
                    if (dynamicList.isNotEmpty &&
                        dynamicList.every((item) => item is ChatMessage)) {
                      final groupedMessages = dynamicList.cast<ChatMessage>();
                      final currentMsg = groupedMessages.first;
                      return GroupChatItemWidget(
                        otherUserId: agentId,
                        userId: userId,
                        buyerId: buyerId,
                        messages: groupedMessages,
                        key: ValueKey(currentMsg.id),
                        onLongPressStart: (details) {
                          if (!(currentMsg.isDeleted ?? false)) {
                            _onLongPress(
                              context,
                              details,
                              currentMsg,
                              _preferences.getUser()?.id ?? -1,
                              userId,
                              buyerId,
                              isSender,
                            );
                          }
                        },
                        isDeleteMode: isDeleteMode && isSender,
                        isSelectedForDeletion:
                            selectedDeletionIds.contains(currentMsg.id ?? -1),
                        onRetrySendMessage: () {
                          final offset = DateTime.now().timeZoneOffset.inHours;
                          final payload = SendMessagePayload(
                            buyerId: buyerId,
                            message: currentMsg.message,
                            userId: userId,
                            otherUserId: agentId,
                            offset: offset,
                          );
                          context.read<ChatBloc>().add(
                              RetrySendMessage(currentMsg.id ?? -1, payload));
                        },
                        onDiscardFailedMessage: () {
                          context
                              .read<ChatBloc>()
                              .add(DiscardFailedMessage(currentMsg.id ?? -1));
                        },
                        onSelectedForDeletion: (id) {
                          onSelectedForDeletion?.call(id);
                        },
                        onTap: isSender && isDeleteMode
                            ? () {
                                onSelectedForDeletion
                                    ?.call(currentMsg.id ?? -1);
                              }
                            : null,
                        avatarPlaceHolderTitle: isSender
                            ? myNameFirstLetters
                            : otherUserNamefirstLetters,
                        id: currentMsg.id ?? -1,
                        isSender: currentMsg.sentBy == userId,
                        imageUrl: currentMsg.sentBy != userId
                            ? agentAvatar
                            : userAvatar,
                        showAvatar: true,
                        hasTail: showAvatar,
                      );
                    }
                  } else if (currentMsg is ChatMessage) {
                    return ChatItemWidget(
                      otherUserId: agentId,
                      userId: userId,
                      buyerId: buyerId,
                      key: ValueKey(currentMsg.id),
                      onLongPressStart: (details) {
                        if (!(currentMsg.isDeleted ?? false)) {
                          _onLongPress(
                              context,
                              details,
                              currentMsg,
                              _preferences.getUser()?.id ?? -1,
                              userId,
                              buyerId,
                              isSender);
                        }
                      },
                      isDeleteMode: isDeleteMode && isSender,
                      isSelectedForDeletion:
                          selectedDeletionIds.contains(currentMsg.id ?? -1),
                      onRetrySendMessage: () {
                        final offset = DateTime.now().timeZoneOffset;
                        final offsetInHours = offset.inHours;
                        final payload = SendMessagePayload(
                          buyerId: buyerId,
                          message: currentMsg.message,
                          userId: userId,
                          otherUserId: agentId,
                          offset: offsetInHours,
                        );
                        context.read<ChatBloc>().add(
                              RetrySendMessage(
                                currentMsg.id ?? -1,
                                payload,
                              ),
                            );
                      },
                      onDiscardFailedMessage: () {
                        context.read<ChatBloc>().add(
                              DiscardFailedMessage(
                                currentMsg.id ?? -1,
                              ),
                            );
                      },
                      onSelectedForDeletion: (id) {
                        onSelectedForDeletion?.call(id);
                      },
                      onSelectedForMultipleDeletion: onMultipleDeletionSelection,
                      onTap: isSender && isDeleteMode
                          ? () {
                              onSelectedForDeletion?.call(currentMsg.id ?? -1);
                            }
                          : null,
                      avatarPlaceHolderTitle: isSender
                          ? myNameFirstLetters
                          : otherUserNamefirstLetters,
                      id: currentMsg.id ?? -1,
                      isSender: isSender,
                      imageUrl: userId != currentMsg.sentBy
                          ? agentAvatar
                          : userAvatar,
                      chatEntity: currentMsg,
                      showAvatar: showAvatar,
                      hasTail: showAvatar,
                    );
                  }
                  return null;
                },
                separatorBuilder: (_, __) => const SizedBox(height: 8),
              ),
            ],
          );
        },
      ),
    );
  }

  List<GroupChatMessage> groupMessagesByDateAndAttachments(
      List<ChatMessage> messages) {
    List<GroupChatMessage> groupedMessages = [];
    final now = DateTime.now();
    Map<String, List<dynamic>> dateGroupedMessages = {};
    for (final message in messages) {
      final messageDate = DateTime.parse(message.timestamp).toLocal();
      String label;
      if (messageDate.year == now.year &&
          messageDate.month == now.month &&
          messageDate.day == now.day) {
        label = 'Today';
      } else if (messageDate.year == now.year &&
          messageDate.month == now.month &&
          messageDate.day == now.day - 1) {
        label = 'Yesterday';
      } else {
        label = DateFormat('MMMM dd, yyyy').format(messageDate);
      }

      if (!dateGroupedMessages.containsKey(label)) {
        dateGroupedMessages[label] = [];
      }
      dateGroupedMessages[label]!.add(message);
    }
    dateGroupedMessages.forEach((label, messagesForDate) {
      List<dynamic> groupedByAttachments = [];
      List<ChatMessage> tempGroup = [];
      for (int i = 0; i < messagesForDate.length; i++) {
        ChatMessage current = messagesForDate[i];
        if (current.isDeleted!) {
          if (tempGroup.isNotEmpty) {
            if (tempGroup.length >= 4) {
              groupedByAttachments.add(List.from(tempGroup));
            } else {
              groupedByAttachments.addAll(tempGroup);
            }
            tempGroup.clear();
          }
          groupedByAttachments.add(current);
          continue;
        }

        if (current.attachments.isNotEmpty && current.message.isEmpty) {
          if (tempGroup.isEmpty || tempGroup.last.sentBy == current.sentBy) {
            tempGroup.add(current);
          } else {
            if (tempGroup.length >= 4) {
              groupedByAttachments.add(List.from(tempGroup));
            } else {
              groupedByAttachments.addAll(tempGroup);
            }
            tempGroup.clear();
            tempGroup.add(current);
          }
        } else {
          if (tempGroup.isNotEmpty) {
            if (tempGroup.length >= 4) {
              groupedByAttachments.add(List.from(tempGroup));
            } else {
              groupedByAttachments.addAll(tempGroup);
            }
            tempGroup.clear();
          }
          groupedByAttachments.add(current);
        }
      }
      if (tempGroup.isNotEmpty) {
        if (tempGroup.length >= 4) {
          groupedByAttachments.add(List.from(tempGroup));
        } else {
          groupedByAttachments.addAll(tempGroup);
        }
      }
      groupedMessages.add(GroupChatMessage(
        label: label,
        messages: groupedByAttachments,
      ));
    });

    return groupedMessages;
  }

  /// Helper function to check if two dates are the same day
  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Convert the grouped map into a list with headers and messages
  List<dynamic> _buildMessageList(
      Map<String, List<ChatMessage>> groupedMessages) {
    final List<dynamic> messageList = [];

    groupedMessages.forEach((dateHeader, messages) {
      // Add the date header (e.g., Today, Yesterday)
      messageList.add(dateHeader);

      // Add the messages under the date header
      messageList.addAll(messages);
    });

    return messageList.reversed.toList();
  }

  void _onLongPress(BuildContext context, LongPressStartDetails details,
      ChatMessage currentMessage, userId, agentId, buyerId, isSender) {
    _showMenu(context, details.globalPosition, currentMessage, userId, agentId,
        buyerId, isSender);
  }

  void _showMenu(BuildContext context, Offset offset,
      ChatMessage currentMessage, userId, agentId, buyerId, isSender) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    showMenu(
      context: context,
      position: RelativeRect.fromRect(
        offset & const Size(40, 40), // Position the menu slightly offset
        Offset.zero & overlay.size,
      ),
      items: isSender
          ? [
              if (currentMessage.message.isNotEmpty)
                PopupMenuItem(
                  padding: EdgeInsets.zero,
                  value: Strings.copyChat,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              Strings.copyChat.toCapitalize(),
                              style: AppTypography.styles(
                                      context.theme.appColors.blackWhite)
                                  .mediumReg
                                  .copyWith(
                                    color: context.theme.appColors.blackWhite,
                                  ),
                            ),
                            Icon(Icons.copy,
                                color: context.theme.appColors.blackWhite),
                          ],
                        ),
                      ),
                      Divider(
                          color: context.theme.appColors.blackWhite
                              .withOpacity(0.4))
                    ],
                  ),
                ),
              if (currentMessage.message.isNotEmpty)
                PopupMenuItem(
                  padding: EdgeInsets.zero,
                  value: Strings.editChat,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              Strings.editChat.toCapitalize(),
                              style: AppTypography.styles(
                                      context.theme.appColors.blackWhite)
                                  .mediumReg
                                  .copyWith(
                                    color: context.theme.appColors.blackWhite,
                                  ),
                            ),
                            Icon(Icons.edit,
                                color: context.theme.appColors.blackWhite),
                          ],
                        ),
                      ),
                      Divider(
                          color: context.theme.appColors.blackWhite
                              .withOpacity(0.4))
                    ],
                  ),
                ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.replyChat,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.replyChat.toCapitalize(),
                            style: AppTypography.styles(
                                    context.theme.appColors.blackWhite)
                                .mediumReg
                                .copyWith(
                                  color: context.theme.appColors.blackWhite,
                                ),
                          ),
                          Icon(Icons.reply_outlined,
                              color: context.theme.appColors.blackWhite),
                        ],
                      ),
                    ),
                    Divider(
                        color:
                            context.theme.appColors.blackWhite.withOpacity(0.4))
                            
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.deleteChat,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        Strings.deleteChat.toCapitalize(),
                        style: AppTypography.styles(
                                context.theme.appColors.blackWhite)
                            .mediumReg
                            .copyWith(
                              color: context.theme.appColors.blackWhite,
                            ),
                      ),
                      Icon(Icons.delete,
                          color: context.theme.appColors.blackWhite),
                    ],
                  ),
                ),
              ),
            ]
          : [
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.copyChat,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.copyChat.toCapitalize(),
                            style: AppTypography.styles(
                                    context.theme.appColors.blackWhite)
                                .mediumReg
                                .copyWith(
                                  color: context.theme.appColors.blackWhite,
                                ),
                          ),
                          Icon(Icons.file_copy_outlined,
                              color: context.theme.appColors.blackWhite),
                        ],
                      ),
                    ),
                    Divider(
                      color:
                          context.theme.appColors.blackWhite.withOpacity(0.4),
                    )
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                value: Strings.replyChat,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        Strings.replyChat.toCapitalize(),
                        style: AppTypography.styles(
                                context.theme.appColors.blackWhite)
                            .mediumReg
                            .copyWith(
                              color: context.theme.appColors.blackWhite,
                            ),
                      ),
                      Icon(Icons.reply_outlined,
                          color: context.theme.appColors.blackWhite),
                    ],
                  ),
                ),
              ),
            ],
    ).then((value) {
      if (value == Strings.copyChat) {
        // Handle delete action
        _copyToClipboard(context, currentMessage.message);
      } else if (value == Strings.deleteChat) {
        // Set<int> singleChatSet = {currentMessage.id!};
        // final payload = DeleteMessagesPayload(
        //   userId: userId,
        //   otherUserId: agentId,
        //   buyerId: buyerId,
        //   messageIds: singleChatSet,
        // );
        if (currentMessage.id != null) {
          onSelectedForDeletion?.call(currentMessage.id!);
        }

        // context.read<ChatBloc>().add(DeleteMessages(payload));
      } else if (value == Strings.replyChat) {
        streamController.add(currentMessage);
        onReplyTap?.call();
      } else if (value == Strings.editChat) {
        editStreamController.add(currentMessage);
        onEditTap?.call(currentMessage.message);
      }
    });
  }

  void _copyToClipboard(BuildContext context, String textToCopy) {
    Clipboard.setData(ClipboardData(text: textToCopy)).then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Strings.messageCopied,
            style: AppTypography.styles(AppColors.white).largeSemi.copyWith(
                  color: AppColors.white,
                ),
          ),
        ),
      );
    });
  }
}

class GroupChatMessage {
  GroupChatMessage({
    required this.label,
    required this.messages,
  });
  final String label;
  final List<dynamic> messages;
}
