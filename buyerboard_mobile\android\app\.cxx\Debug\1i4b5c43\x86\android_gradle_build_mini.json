{"buildFiles": ["P:\\flutter_sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "P:\\Buyerboard\\buyerboard_mobile\\android\\app\\.cxx\\Debug\\1i4b5c43\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "P:\\Buyerboard\\buyerboard_mobile\\android\\app\\.cxx\\Debug\\1i4b5c43\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}