// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_buyer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatGroupModel _$ChatGroupModelFromJson(Map<String, dynamic> json) =>
    ChatGroupModel(
      threads: (json['threads'] as List<dynamic>?)
              ?.map((e) =>
                  ChatGroupThreadModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      sku: json['sku'] as String? ?? '',
      myBuyer: json['my_buyer'] as bool? ?? false,
      buyerId: (json['buyer_id'] as num).toInt(),
      id: (json['id'] as num?)?.toInt(),
      buyersAlias: json['buyers_alias'] as String?,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
    );

Map<String, dynamic> _$ChatGroupModelToJson(ChatGroupModel instance) =>
    <String, dynamic>{
      'threads': instance.threads.map((e) => e.toJson()).toList(),
      'sku': instance.sku,
      'my_buyer': instance.myBuyer,
      'id': instance.id,
      'buyer_id': instance.buyerId,
      'buyers_alias': instance.buyersAlias,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
    };

ChatGroupThreadModel _$ChatGroupThreadModelFromJson(
        Map<String, dynamic> json) =>
    ChatGroupThreadModel(
      id: (json['id'] as num?)?.toInt(),
      agentId: (json['agent_id'] as num).toInt(),
      avatarUrl: json['avatar_url'] as String?,
      name: json['name'] as String?,
      lastMessage: json['latest_message'] == null
          ? null
          : ChatMessage.fromJson(
              json['latest_message'] as Map<String, dynamic>),
      timeStamp: json['time_stamp'] as String?,
      hasNewMessage: json['has_new_message'] as bool? ?? false,
      userId: (json['user_id'] as num).toInt(),
      agentImageUrl: json['agent_image_url'] as String?,
      userImageUrl: json['user_image_url'] as String?,
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ChatGroupThreadModelToJson(
        ChatGroupThreadModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'agent_id': instance.agentId,
      'avatar_url': instance.avatarUrl,
      'name': instance.name,
      'latest_message': instance.lastMessage?.toJson(),
      'user': instance.user?.toJson(),
      'time_stamp': instance.timeStamp,
      'user_id': instance.userId,
      'has_new_message': instance.hasNewMessage,
      'agent_image_url': instance.agentImageUrl,
      'user_image_url': instance.userImageUrl,
    };
