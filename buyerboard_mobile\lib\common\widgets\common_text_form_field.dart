import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/text_field_formatters.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CommonTextFormField extends StatefulWidget {
  const CommonTextFormField({
    super.key,
    this.label,
    this.hint,
    this.errorText,
    this.suffixIcon,
    this.obscure = false,
    this.controller,
    this.validator,
    this.onChanged,
    this.onClear,
    this.error = true,
    this.fillColor,
    this.onTap,
    this.readOnly = false,
    this.keyboardType,
    this.borderRadius = BorderRadius.zero,
    this.errorTextColor,
    this.outlineErrorBorder = false,
    this.showErrorPrefixIcon = false,
    this.errorContentTextColor,
    this.contextTextColor,
    this.labelStyle,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
    this.hintTextStyle,
    this.contextTextStyle,
    this.borderColor,
    this.inputFormatters,
    this.prefixIcon,
    this.textCapitalization = TextCapitalization.none,
  });
  final String? label;
  final String? hint;
  final String? errorText;
  final bool obscure;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final Function(String? val)? onChanged;
  final VoidCallback? onClear;
  final bool error;
  final Color? fillColor;
  final bool readOnly;
  final TextInputType? keyboardType;
  final VoidCallback? onTap;
  final BorderRadius borderRadius;
  final Color? errorTextColor;
  final bool outlineErrorBorder;
  final bool showErrorPrefixIcon;
  final Color? errorContentTextColor;
  final Color? contextTextColor;
  final TextStyle? labelStyle;
  final EdgeInsetsGeometry padding;
  final TextStyle? hintTextStyle;
  final TextStyle? contextTextStyle;
  final Color? borderColor;
  final Widget? prefixIcon;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization textCapitalization;

  @override
  State<CommonTextFormField> createState() => _CommonTextFormFieldState();
}

class _CommonTextFormFieldState extends State<CommonTextFormField> {
  late final bool _isPassField;
  late bool _obscure;
  String? errorText;
  @override
  void initState() {
    super.initState();
    _isPassField = widget.keyboardType == TextInputType.visiblePassword;
    _obscure = widget.obscure || _isPassField;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.theme.colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null)
          Text(
            widget.label!,
            style: widget.labelStyle ??
                AppStyles.small.copyWith(color: AppColors.primaryLight),
          ),
        TextFormField(
          style: widget.contextTextStyle ??
              AppStyles.small.copyWith(
                  color: (widget.errorContentTextColor != null &&
                          errorText != null)
                      ? widget.errorContentTextColor
                      : widget.contextTextColor ?? colorScheme.onSurface),
          onTap: widget.onTap,

          controller: widget.controller,
          inputFormatters: [
            if (widget.textCapitalization == TextCapitalization.sentences)
              UpperCaseTextFormatter(),
            ...widget.inputFormatters ?? [],
          ],
          obscureText: _obscure,

          validator: (value) {
            if (widget.validator != null) {
              errorText = widget.validator!(value);
              setState(() => errorText = errorText);
              return errorText;
            }
            setState(() {
              errorText = null;
            });
            return null;
          },
          cursorColor: widget.contextTextColor ?? colorScheme.onSurface,
          cursorErrorColor: widget.errorContentTextColor != null
              ? AppColors.errorMedium
              : widget.contextTextColor ?? colorScheme.onSurface,
          // (widget.errorContentTextColor != null && errorText != null)
          //     ? widget.errorContentTextColor
          //     : widget.contextTextColor ?? AppColors.white,
          readOnly: widget.readOnly,
          keyboardType: widget.keyboardType,
          textCapitalization: widget.textCapitalization,
          onChanged: widget.onChanged,
          cursorHeight: 18,
          decoration: InputDecoration(
            // isDense: true,
            prefixIcon: widget.prefixIcon,
            prefixIconConstraints: const BoxConstraints(
              minWidth: 20,
              minHeight: 10,
            ),
            filled: true,
            isCollapsed: true,
            fillColor: errorText != null && widget.outlineErrorBorder
                ? AppColors.errorXLight
                : widget.fillColor ?? AppColors.primaryMedium,
            hintText: widget.hint,
            hintStyle: widget.hintTextStyle ??
                AppStyles.small.copyWith(color: AppColors.grey),
            errorStyle: AppStyles.small.copyWith(
                color: widget.errorTextColor ?? AppColors.errorMedium,
                fontSize: 11,
                fontWeight: FontWeight.w700),
            errorText: widget.errorText,
            focusedBorder: UnderlineInputBorder(
              borderRadius: widget.borderRadius,
              borderSide: BorderSide(
                color: widget.borderColor ?? AppColors.primaryLight,
              ),
            ),
            contentPadding: widget.padding,
            suffixIconConstraints: const BoxConstraints(
              minWidth: 34,
              minHeight: 24,
            ),
            suffixIcon: widget.showErrorPrefixIcon && errorText != null
                ? const Icon(
                    Icons.warning,
                    color: AppColors.errorMedium,
                    size: 16,
                  )
                : widget.suffixIcon ??
                    (_isPassField
                        ? GestureDetector(
                            onTap: () => setState(() => _obscure = !_obscure),
                            child: Container(
                              color: Colors.transparent,
                              height: 24,
                              width: 24,
                              child: Icon(
                                _obscure
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: AppColors.white,
                                size: 16,
                              ),
                            ),
                          )
                        : null),
            enabledBorder: UnderlineInputBorder(
              borderRadius: widget.borderRadius,
              borderSide: BorderSide(
                color: widget.borderColor ?? AppColors.primaryLight,
              ),
            ),
            focusedErrorBorder: widget.outlineErrorBorder
                ? OutlineInputBorder(
                    borderRadius: widget.borderRadius,
                    borderSide: const BorderSide(
                      color: AppColors.error,
                    ),
                  )
                : UnderlineInputBorder(
                    borderRadius: widget.borderRadius,
                    borderSide: const BorderSide(
                      color: AppColors.error,
                    ),
                  ),
            border: OutlineInputBorder(
              borderRadius: widget.borderRadius,
              borderSide: BorderSide(
                color: widget.borderColor ?? AppColors.primaryLight,
              ),
            ),
            errorBorder: widget.outlineErrorBorder
                ? OutlineInputBorder(
                    borderRadius: widget.borderRadius,
                    borderSide: const BorderSide(
                      color: AppColors.error,
                    ),
                  )
                : UnderlineInputBorder(
                    borderRadius: widget.borderRadius,
                    borderSide: const BorderSide(
                      color: AppColors.error,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
