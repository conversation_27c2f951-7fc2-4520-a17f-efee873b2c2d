import 'package:buyer_board/features/settings/data/datasources/settings_data_source.dart';
import 'package:buyer_board/features/settings/domain/repositories/settings_repository.dart';

class SettingsRepositoryImpl implements SettingsRepository {
  SettingsRepositoryImpl({required this.dataSource});
  final SettingsDataSource dataSource;
  @override
  Future<void> deleteAccount() async {
    await dataSource.deleteAccount();
  }
}
