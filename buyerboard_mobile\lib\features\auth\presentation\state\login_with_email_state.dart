import 'package:buyer_board/features/auth/data/models/requests/auth_request.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../data/models/response/auth_response.dart';
part 'login_with_email_state.freezed.dart';

@freezed
class LoginWithEmailState with _$LoginWithEmailState {
  const factory LoginWithEmailState.initial() = initial;
  const factory LoginWithEmailState.loading() = loading;
  const factory LoginWithEmailState.success(
      User authResponse, String message, String route) = success;
  const factory LoginWithEmailState.localAuthSuccess(AuthRequest authRequest) =
      localAuthSuccess;
  const factory LoginWithEmailState.rememberMe(String message, String route) =
      rememberMe;
  const factory LoginWithEmailState.loginError(String? error) = loginError;
}
