import 'package:buyer_board/common/widgets/profile_text_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/features/profile/presentation/cubit/address_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/resources/resources.dart';
import '../../../../core/utils/validators.dart';

class BrokerageInformationSection extends StatefulWidget {
  const BrokerageInformationSection({
    super.key,
    required this.brokerageNameController,
    required this.brokerageLicenseNumberController,
    required this.brokerageStreetAddressController,
    required this.brokerageCityController,
    required this.brokerageStateController,
    required this.zipCodeController,
    this.addressError,
    this.readOnly = true,
  });
  final TextEditingController brokerageNameController;
  final TextEditingController brokerageLicenseNumberController;
  final TextEditingController brokerageStreetAddressController;
  final TextEditingController brokerageCityController;
  final TextEditingController brokerageStateController;
  final TextEditingController zipCodeController;
  final String? addressError;
  final bool readOnly;

  @override
  State<BrokerageInformationSection> createState() =>
      _BrokerageInformationSectionState();
}

class _BrokerageInformationSectionState
    extends State<BrokerageInformationSection> {
  @override
  void initState() {
    super.initState();
    widget.zipCodeController.addListener(() {
      if (widget.zipCodeController.text.length < 5) {
        widget.brokerageCityController.clear();
        widget.brokerageStateController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.brokerageInformation,
          style: context.typography.large1xBlack.copyWith(
            color: context.colorScheme.onSurface,
          ),
        ),
        spacerH8,
        ProfileTextField(
          controller: widget.brokerageNameController,
          validator: Validators().requiredBrokageNameValidator,
          textCapitalization: TextCapitalization.sentences,
          label: Strings.brokerageName,
          hint: Strings.brokerageNameHint,
          readOnly: widget.readOnly,
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: widget.brokerageLicenseNumberController,
          // validator: Validators().requiredFieldValidator,
          validator: CoreUtils.brokerageLicenseNumber(),
          label: Strings.brokerageLicenseNumber,
          hint: Strings.brokerageLicenseNumberHint,
          readOnly: widget.readOnly,
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: widget.brokerageStreetAddressController,
          label: Strings.brokerageStreetAddress,
          hint: Strings.brokerageStreetAddressHint,
          textCapitalization: TextCapitalization.sentences,
          readOnly: widget.readOnly,
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          controller: widget.zipCodeController,
          validator: Validators().zipcodeFieldValidator,
          keyboardType: const TextInputType.numberWithOptions(
            signed: false,
            decimal: false,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(5),
          ],
          label: Strings.brokerageZipCode,
          hint: Strings.brokerageZipCodeHint,
          errorText: widget.addressError,
          onChanged: (val) {
            if (val != null && val.length > 4) {
              context.read<AddressCubit>().getAddressFromZipCode(zipCode: val);
            }
          },
          readOnly: widget.readOnly,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Flexible(
              flex: 3,
              child: ProfileTextField(
                controller: widget.brokerageCityController,
                validator: Validators().requiredFieldValidator,
                label: Strings.brokerageCity,
                hint: Strings.brokerageCityHint,
                readOnly: true,
              ),
            ),
            const SizedBox(width: 16),
            Flexible(
              flex: 1,
              child: ProfileTextField(
                controller: widget.brokerageStateController,
                validator: Validators().requiredFieldValidator,
                label: Strings.state,
                hint: Strings.state,
                readOnly: true,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
