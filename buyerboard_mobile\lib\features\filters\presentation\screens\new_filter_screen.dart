import 'package:buyer_board/common/widgets/app_checkbox_tile.dart';
import 'package:buyer_board/common/widgets/app_switch_tile.dart';
import 'package:buyer_board/common/widgets/common_radio_tile.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/text_field_formatters.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/property_attribute.dart';
import 'package:buyer_board/features/filters/models/new_filter_model.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_cubit.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_selection_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

ValueNotifier<bool> isFilteringNotifier = ValueNotifier(false);

class NewFilterScreen extends StatefulWidget {
  const NewFilterScreen({super.key});

  @override
  State<NewFilterScreen> createState() => _NewFilterScreenState();
}

class _NewFilterScreenState extends State<NewFilterScreen> {
  @override
  void initState() {
    super.initState();
    final data = context.read<NewFilterCubit>();
    // Future.microtask(() {
    if (data.state.filters != null) {
      context
          .read<NewFilterSelectionCubit>()
          .setFiltersOption(data.state.filters!);
    }
    if (data.state.sortOptions != null) {
      context
          .read<NewFilterSelectionCubit>()
          .setSortOption(data.state.sortOptions!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Filtering'),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(Dimensions.materialPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const _ActionButtons(),
              spacerH16,
              _heading(colors),
              spacerH4,
              _subHeading(colors),
              spacerH8,
              BlocBuilder<NewFilterSelectionCubit, NewFilterModel>(
                builder: (context, state) {
                  return AppSwitch(
                    title: 'Hide My Buyers',
                    isActive: state.filters?.hideMyBuyers ?? false,
                    onChanged: (_) {
                      context
                          .read<NewFilterSelectionCubit>()
                          .toggleHideMyBuyers();
                    },
                  );
                },
              ),
              spacerH8,
              const _PurchaseTypeFilter(),
              spacerH16,
              const _PropertyTypeFilter(),
              spacerH16,
              const _FinancialStatusFilter(),
              // spacerH16,
              // const _TimelineFilter(),
              spacerH16,
              const _OtherPropertyInfoFilters(),
            ],
          ),
        ),
      ),
    );
  }

  Text _heading(ColorScheme colors) {
    return Text(
      'Filters',
      style: AppStyles.sectionHeading.copyWith(
        color: colors.onSurface,
      ),
    );
  }

  Text _subHeading(ColorScheme colors) {
    return Text(
      'Choose from the filters below',
      style: AppStyles.sectionSubHeading.copyWith(
        color: colors.onSurface,
      ),
    );
  }
}

class _ActionButtons extends StatelessWidget {
  const _ActionButtons();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: FilledButton(
            child: const Text('Apply Filters'),
            onPressed: () => _onApplyFilters(context),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton(
            style: OutlinedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            child: const Text('Clear Filters'),
            onPressed: () => _onClearFilters(context),
          ),
        ),
      ],
    );
  }

  void _onApplyFilters(BuildContext context) {
    final filters = context.read<NewFilterSelectionCubit>();
    context.read<NewFilterCubit>().setFiltersOption(filters.state.filters!);
    context.read<NewFilterCubit>().setSortOption(filters.state.sortOptions!);
    Future.microtask(() {
      context
          .read<BuyersBloc>()
          .add(LoadBuyers(forceRefresh: true, context: context));
    });

    isFilteringNotifier.value = true;

    context.pop();
  }

  void _onClearFilters(BuildContext context) {
    isFilteringNotifier.value = false;
    context.read<NewFilterSelectionCubit>().clearFilters();
    context.read<NewFilterCubit>().clearFilters();
    Future.microtask(() {
      context
          .read<BuyersBloc>()
          .add(LoadBuyers(forceRefresh: true, context: context));
    });

    context.pop();
  }
}

class _PurchaseTypeFilter extends StatelessWidget {
  const _PurchaseTypeFilter();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Purchase Type',
          style: AppStyles.small.copyWith(
            color: context.colorScheme.onSurfaceVariant,
          ),
        ),
        spacerH12,
        BlocBuilder<NewFilterSelectionCubit, NewFilterModel>(
            builder: (_, state) {
          return Wrap(
            children: PurchaseType.values
                .map(
                  (e) => CommonRadioTile<PurchaseType?>(
                    value: e,
                    label: e.label,
                    groupValue: state.filters?.purchaseType,
                    onChange: (val) {
                      context
                          .read<NewFilterSelectionCubit>()
                          .setPurchaseType(val!);
                    },
                  ),
                )
                .toList(),
          );
        }),
      ],
    );
  }
}

class _PropertyTypeFilter extends StatelessWidget {
  const _PropertyTypeFilter();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Property Type',
          style: AppStyles.small.copyWith(
            color: context.colorScheme.onSurfaceVariant,
          ),
        ),
        spacerH12,
        BlocBuilder<NewFilterSelectionCubit, NewFilterModel>(
          builder: (_, state) {
            return Wrap(
              spacing: 12,
              children: PropertyType.values
                  .map(
                    (e) => AppCheckboxTile<PropertyType?>(
                      value: state.filters?.propertyType?.contains(e) ?? false,
                      label: e.label,
                      onChanged: (val) {
                        final propertyTypes = state.filters?.propertyType ?? [];
                        if (propertyTypes.contains(e)) {
                          propertyTypes.remove(e);
                        } else {
                          propertyTypes.add(e);
                        }
                        context
                            .read<NewFilterSelectionCubit>()
                            .setPropertyType(propertyTypes);
                      },
                    ),
                  )
                  .toList(),
            );
          },
        ),
      ],
    );
  }
}

class _FinancialStatusFilter extends StatelessWidget {
  const _FinancialStatusFilter();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Financial Status',
          style: AppStyles.small.copyWith(
            color: context.colorScheme.onSurfaceVariant,
          ),
        ),
        spacerH12,
        BlocBuilder<NewFilterSelectionCubit, NewFilterModel>(
          builder: (_, state) {
            return Wrap(
              spacing: 12,
              children: FinancialStatus.values
                  .map(
                    (e) => AppCheckboxTile<FinancialStatus?>(
                      value:
                          state.filters?.financialStatus?.contains(e) ?? false,
                      label: e.label,
                      onChanged: (val) {
                        final financialStatuses =
                            state.filters?.financialStatus ?? [];
                        if (financialStatuses.contains(e)) {
                          financialStatuses.remove(e);
                        } else {
                          financialStatuses.add(e);
                        }
                        context
                            .read<NewFilterSelectionCubit>()
                            .setFinancialStatus(financialStatuses);
                      },
                    ),
                  )
                  .toList(),
            );
          },
        ),
      ],
    );
  }
}

class _OtherPropertyInfoFilters extends StatefulWidget {
  const _OtherPropertyInfoFilters();

  @override
  State<_OtherPropertyInfoFilters> createState() =>
      _OtherPropertyInfoFiltersState();
}

class _OtherPropertyInfoFiltersState extends State<_OtherPropertyInfoFilters> {
  late final TextEditingController minimumBedroomsController;
  late final TextEditingController minimumBathroomsController;
  late final TextEditingController minimumSquareFootageController;

  @override
  void initState() {
    super.initState();

    final state = context.read<NewFilterSelectionCubit>().state;

    minimumBedroomsController =
        TextEditingController(text: state.filters?.minBedrooms?.toString());
    minimumBathroomsController =
        TextEditingController(text: state.filters?.minBathrooms?.toString());
    minimumSquareFootageController =
        TextEditingController(text: state.filters?.minArea?.toString());
  }

  @override
  void dispose() {
    minimumBedroomsController.dispose();
    minimumBathroomsController.dispose();
    minimumSquareFootageController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PropertyAttribute(
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(4),
                FilteringTextInputFormatter.deny(
                  RegExp(r'^0+'),
                ),
          ],
          onChanged: (bedroomCount) => context
              .read<NewFilterSelectionCubit>()
              .setMinBedrooms(double.tryParse(bedroomCount ?? 'null')),
          attributeController: minimumBedroomsController,
          label: Strings.bedroomsMin,
          icon: SvgPicture.asset(
            Drawables.icBedroomOutlined,
            colorFilter: ColorFilter.mode(
              colors.onPrimary,
              BlendMode.srcIn,
            ),
            width: 24,
            height: 24,
          ),
        ),
        const SizedBox(width: 12),
        PropertyAttribute(
          inputFormatters: [
             LengthLimitingTextInputFormatter(5),
                FilteringTextInputFormatter.allow(
                  RegExp(r'^(?!0)\d*\.?\d*'),
                ),
          ],
          onChanged: (bathroomCount) {
            final value = bathroomCount == '.' ? '0.0' : bathroomCount;
            context
                .read<NewFilterSelectionCubit>()
                .setMinBathrooms(double.tryParse(value ?? 'null'));
          },
          attributeController: minimumBathroomsController,
          label: Strings.bathroomsMin,
          icon: SvgPicture.asset(
            Drawables.icBathroomOutlined,
            colorFilter: ColorFilter.mode(
              colors.onPrimary,
              BlendMode.srcIn,
            ),
            width: 24,
            height: 24,
          ),
        ),
        const SizedBox(width: 12),
        PropertyAttribute(
          inputFormatters: [
           FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
                USCurrencyInputFormatter(symbol: ''),
                FilteringTextInputFormatter.deny(
                  RegExp(r'^0+'),
                ),
          ],
          onChanged: (area) =>
              context.read<NewFilterSelectionCubit>().setMinArea(
                    double.tryParse(area ?? 'null'),
                  ),
          attributeController: minimumSquareFootageController,
          label: Strings.sqFootageMin,
          icon: SvgPicture.asset(
            Drawables.icAreaOutlined,
            colorFilter: ColorFilter.mode(
              colors.onPrimary,
              BlendMode.srcIn,
            ),
            width: 24,
            height: 24,
          ),
        ),
      ],
    );
  }
}
