# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

with dotenv = "#{__DIR__}/../.env",
     {:ok, data} <- File.read(dotenv),
     do:
       for(
         "export" <> kv <- String.split(data, "\n"),
         [k, v] = String.split(kv, "=", parts: 2),
         do:
           k
           |> String.trim()
           |> System.put_env(
             v
             # Trim spaces
             |> String.trim()
             # Remove leading quotes
             |> String.trim_leading("\"")
             # Remove trailing quotes
             |> String.trim_trailing("\"")
             # Remove trailing backslash if any
             |> String.trim_trailing("\\")
             # Remove trailing carriage return
             |> String.trim_trailing("\r")
             # Remove trailing newline
             |> String.trim_trailing("\n")
           )
       )

config :buyerboard_backend,
  ecto_repos: [BuyerboardBackend.Repo],
  generators: [timestamp_type: :utc_datetime]

# Configures the endpoint
config :buyerboard_backend, BuyerboardBackendWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: BuyerboardBackendWeb.ErrorHTML, json: BuyerboardBackendWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: BuyerboardBackend.PubSub,
  live_view: [signing_salt: "+DxpEMlU"]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
config :buyerboard_backend, BuyerboardBackend.Mailer, adapter: Swoosh.Adapters.Local

config :buyerboard_backend, :phoenix_swagger,
  swagger_files: %{
    "priv/static/swagger.json" => [
      router: BuyerboardBackendWeb.Router,
      endpoint: BuyerboardBackendWeb.Endpoint
    ]
  }

config :arc,
  storage: Arc.Storage.Local,
  storage_dir: "uploads"

config :phoenix_swagger, json_library: Jason
# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  buyerboard_backend: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "3.4.0",
  buyerboard_backend: [
    args: ~w(
    --config=tailwind.config.js
--input=css/app.css
--output=../priv/static/assets/app.css
),
    cd: Path.expand("../assets", __DIR__)
  ]

# Configures Elixir's Logger
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
