defmodule BuyerboardBackend.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      BuyerboardBackendWeb.Telemetry,
      BuyerboardBackend.Repo,
      {DNSCluster,
       query: Application.get_env(:buyerboard_backend, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: BuyerboardBackend.PubSub},
      # Start the Finch HTTP client for sending emails
      {Finch, name: BuyerboardBackend.Finch},
      # Start a worker by calling: BuyerboardBackend.Worker.start_link(arg)
      # {BuyerboardBackend.Worker, arg},
      # Start to serve requests, typically the last entry
      BuyerboardBackendWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: BuyerboardBackend.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    BuyerboardBackendWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
