import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'favourite_buyer_state.freezed.dart';

@freezed
class FavouriteBuyerState with _$FavouriteBuyerState {
  const factory FavouriteBuyerState.initial() = initial;
  const factory FavouriteBuyerState.loading() = loading;
  const factory FavouriteBuyerState.toggleInProgress() = toggleInProgress;
  const factory FavouriteBuyerState.success(
      {required List<BuyerModel> buyers, required String message}) = success;
  const factory FavouriteBuyerState.toggleSuccess(
      {required BuyerModel buyer, required String message}) = toggleSuccess;
  const factory FavouriteBuyerState.favouriteBuyersError(String? error) =
      favouriteBuyersError;
  const factory FavouriteBuyerState.toggleError(String? error) = toggleError;
}
