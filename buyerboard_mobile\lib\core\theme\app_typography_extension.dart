// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

class AppTypographyExtension extends ThemeExtension<AppTypographyExtension> {
  final TextStyle large5xReg;
  final TextStyle large5xSemi;
  final TextStyle large5xBlack;
  final TextStyle large4xReg;
  final TextStyle large4xSemi;
  final TextStyle large4xBlack;
  final TextStyle large3xReg;
  final TextStyle large3xSemi;
  final TextStyle large3xBlack;
  final TextStyle large2xReg;
  final TextStyle large2xSemi;
  final TextStyle large2xBlack;
  final TextStyle large1xReg;
  final TextStyle large1xSemi;
  final TextStyle large1xBlack;
  final TextStyle largeReg;
  final TextStyle largeSemi;
  final TextStyle largeBlack;
  final TextStyle mediumReg;
  final TextStyle mediumSemi;
  final TextStyle mediumBlack;
  final TextStyle smallReg;
  final TextStyle smallSemi;
  final TextStyle smallBlack;
  final TextStyle small1xReg;
  final TextStyle small1xSemi;
  final TextStyle small1xBlack;
  final TextStyle small2xReg;
  final TextStyle small2xSemi;
  final TextStyle small2xBlack;

  AppTypographyExtension({
    required this.large5xReg,
    required this.large5xSemi,
    required this.large5xBlack,
    required this.large4xReg,
    required this.large4xSemi,
    required this.large4xBlack,
    required this.large3xReg,
    required this.large3xSemi,
    required this.large3xBlack,
    required this.large2xReg,
    required this.large2xSemi,
    required this.large2xBlack,
    required this.large1xReg,
    required this.large1xSemi,
    required this.large1xBlack,
    required this.largeReg,
    required this.largeSemi,
    required this.largeBlack,
    required this.mediumReg,
    required this.mediumSemi,
    required this.mediumBlack,
    required this.smallReg,
    required this.smallSemi,
    required this.smallBlack,
    required this.small1xReg,
    required this.small1xSemi,
    required this.small1xBlack,
    required this.small2xReg,
    required this.small2xSemi,
    required this.small2xBlack,
  });

  @override
  AppTypographyExtension copyWith({
    TextStyle? large5xReg,
    TextStyle? large5xSemi,
    TextStyle? large5xBlack,
    TextStyle? large4xReg,
    TextStyle? large4xSemi,
    TextStyle? large4xBlack,
    TextStyle? large3xReg,
    TextStyle? large3xSemi,
    TextStyle? large3xBlack,
    TextStyle? large2xReg,
    TextStyle? large2xSemi,
    TextStyle? large2xBlack,
    TextStyle? large1xReg,
    TextStyle? large1xSemi,
    TextStyle? large1xBlack,
    TextStyle? largeReg,
    TextStyle? largeSemi,
    TextStyle? largeBlack,
    TextStyle? mediumReg,
    TextStyle? mediumSemi,
    TextStyle? mediumBlack,
    TextStyle? smallReg,
    TextStyle? smallSemi,
    TextStyle? smallBlack,
    TextStyle? small1xReg,
    TextStyle? small1xSemi,
    TextStyle? small1xBlack,
    TextStyle? small2xReg,
    TextStyle? small2xSemi,
    TextStyle? small2xBlack,
  }) {
    return AppTypographyExtension(
      large5xReg: large5xReg ?? this.large5xReg,
      large5xSemi: large5xSemi ?? this.large5xSemi,
      large5xBlack: large5xBlack ?? this.large5xBlack,
      large4xReg: large4xReg ?? this.large4xReg,
      large4xSemi: large4xSemi ?? this.large4xSemi,
      large4xBlack: large4xBlack ?? this.large4xBlack,
      large3xReg: large3xReg ?? this.large3xReg,
      large3xSemi: large3xSemi ?? this.large3xSemi,
      large3xBlack: large3xBlack ?? this.large3xBlack,
      large2xReg: large2xReg ?? this.large2xReg,
      large2xSemi: large2xSemi ?? this.large2xSemi,
      large2xBlack: large2xBlack ?? this.large2xBlack,
      large1xReg: large1xReg ?? this.large1xReg,
      large1xSemi: large1xSemi ?? this.large1xSemi,
      large1xBlack: large1xBlack ?? this.large1xBlack,
      largeReg: largeReg ?? this.largeReg,
      largeSemi: largeSemi ?? this.largeSemi,
      largeBlack: largeBlack ?? this.largeBlack,
      mediumReg: mediumReg ?? this.mediumReg,
      mediumSemi: mediumSemi ?? this.mediumSemi,
      mediumBlack: mediumBlack ?? this.mediumBlack,
      smallReg: smallReg ?? this.smallReg,
      smallSemi: smallSemi ?? this.smallSemi,
      smallBlack: smallBlack ?? this.smallBlack,
      small1xReg: small1xReg ?? this.small1xReg,
      small1xSemi: small1xSemi ?? this.small1xSemi,
      small1xBlack: small1xBlack ?? this.small1xBlack,
      small2xReg: small2xReg ?? this.small2xReg,
      small2xSemi: small2xSemi ?? this.small2xSemi,
      small2xBlack: small2xBlack ?? this.small2xBlack,
    );
  }

  @override
  ThemeExtension<AppTypographyExtension> lerp(
      covariant ThemeExtension<AppTypographyExtension>? other, double t) {
    if (other is! AppTypographyExtension) {
      return this;
    }
    return AppTypographyExtension(
      large5xReg: TextStyle.lerp(large5xReg, other.large5xReg, t)!,
      large5xSemi: TextStyle.lerp(large5xSemi, other.large5xSemi, t)!,
      large5xBlack: TextStyle.lerp(large5xBlack, other.large5xBlack, t)!,
      large4xReg: TextStyle.lerp(large4xReg, other.large4xReg, t)!,
      large4xSemi: TextStyle.lerp(large4xSemi, other.large4xSemi, t)!,
      large4xBlack: TextStyle.lerp(large4xBlack, other.large4xBlack, t)!,
      large3xReg: TextStyle.lerp(large3xReg, other.large3xReg, t)!,
      large3xSemi: TextStyle.lerp(large3xSemi, other.large3xSemi, t)!,
      large3xBlack: TextStyle.lerp(large3xBlack, other.large3xBlack, t)!,
      large2xReg: TextStyle.lerp(large2xReg, other.large2xReg, t)!,
      large2xSemi: TextStyle.lerp(large2xSemi, other.large2xSemi, t)!,
      large2xBlack: TextStyle.lerp(large2xBlack, other.large2xBlack, t)!,
      large1xReg: TextStyle.lerp(large1xReg, other.large1xReg, t)!,
      large1xSemi: TextStyle.lerp(large1xSemi, other.large1xSemi, t)!,
      large1xBlack: TextStyle.lerp(large1xBlack, other.large1xBlack, t)!,
      largeReg: TextStyle.lerp(largeReg, other.largeReg, t)!,
      largeSemi: TextStyle.lerp(largeSemi, other.largeSemi, t)!,
      largeBlack: TextStyle.lerp(largeBlack, other.largeBlack, t)!,
      mediumReg: TextStyle.lerp(mediumReg, other.mediumReg, t)!,
      mediumSemi: TextStyle.lerp(mediumSemi, other.mediumSemi, t)!,
      mediumBlack: TextStyle.lerp(mediumBlack, other.mediumBlack, t)!,
      smallReg: TextStyle.lerp(smallReg, other.smallReg, t)!,
      smallSemi: TextStyle.lerp(smallSemi, other.smallSemi, t)!,
      smallBlack: TextStyle.lerp(smallBlack, other.smallBlack, t)!,
      small1xReg: TextStyle.lerp(small1xReg, other.small1xReg, t)!,
      small1xSemi: TextStyle.lerp(small1xSemi, other.small1xSemi, t)!,
      small1xBlack: TextStyle.lerp(small1xBlack, other.small1xBlack, t)!,
      small2xReg: TextStyle.lerp(small2xReg, other.small2xReg, t)!,
      small2xSemi: TextStyle.lerp(small2xSemi, other.small2xSemi, t)!,
      small2xBlack: TextStyle.lerp(small2xBlack, other.small2xBlack, t)!,
    );
  }
}
