import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:flutter/material.dart';

class ExpansionCollapseButtonsWidget extends StatelessWidget {
  const ExpansionCollapseButtonsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FilledButton(
          child: Text(
            Strings.expandAll,
            style: context.typography.mediumSemi.copyWith(
              color: context.appColors.whitePXDark,
            ),
          ),
          onPressed: () {},
        ),
        spacerW16,
        FilledButton(
          child: Text(
            Strings.collapseAll,
            style: context.typography.mediumSemi.copyWith(
              color: context.appColors.whitePXDark,
            ),
          ),
          onPressed: () {},
        ),
      ],
    );
  }
}
