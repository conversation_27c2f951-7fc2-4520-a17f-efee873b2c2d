defmodule BuyerboardBackendWeb.Plugs.CheckAccountStatus do
  import Plug.Conn
  alias BuyerboardBackend.Accounts

  def init(opts), do: opts

  def call(conn, _opts) do
    email = conn.body_params["email"] || conn.params["email"]

    if email do
      case Accounts.get_user_by_email(email) do
        %{is_active: false} ->
          conn
          |> put_status(:forbidden)
          |> put_resp_content_type("application/json")
          |> Phoenix.Controller.json(%{
            error: %{message: "Account is disabled. Please contact support or use a different email."}
          })
          |> halt()

        _user -> conn
      end
    else
      conn
    end
  end
end
