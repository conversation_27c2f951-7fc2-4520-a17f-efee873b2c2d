import 'dart:async';

import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/common/widgets/common_text_form_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:buyer_board/features/auth/presentation/screens/auth_screen.dart';
import 'package:buyer_board/features/auth/presentation/state/login_with_email_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/utils/on_widget_built_mixin.dart';
import '../cubit/login_with_email_cubit.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key, this.localAuthEnabled = false});
  final bool localAuthEnabled;

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> with WidgetBuiltMixin {
  final emailTextController = TextEditingController();
  final passwordTextController = TextEditingController();
  bool rememberMe = false;
  final _formKey = GlobalKey<FormState>();
  void toggleRememberMe(bool? val) {
    setState(() {
      rememberMe = val ?? rememberMe;
    });
  }

  void onLogin(BuildContext context) {
    final String email = emailTextController.text;
    final password = passwordTextController.text;
    context.read<LoginWithEmailCubit>().loginWithEmailAndPassword(
          context,
          email: email.trim(),
          password: password,
          rememberMe: rememberMe,
        );
  }

  @override
  void initState() {
    super.initState();

    if (kDebugMode) {
      emailTextController.text = '<EMAIL>';
      passwordTextController.text = 'Temp/@123';
    }
  }

  @override
  void dispose() {
    emailTextController.dispose();
    passwordTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginWithEmailCubit, LoginWithEmailState>(
      listener: (context, state) {
        state.mapOrNull(
          loading: (state) => Loader.show(),
          localAuthSuccess: (state) {
            Loader.show();
            context.read<LoginWithEmailCubit>().loginWithEmailAndPassword(
                  context,
                  email: state.authRequest.email,
                  password: state.authRequest.password,
                  rememberMe: false,
                );
          },
          success: (state) {
            Loader.hide();
            context
              ..showToast(message: state.message, isSuccess: true)
              ..go(state.route);
          },
          rememberMe: (state) {
            Loader.hide();
            context
              ..showToast(message: state.message, isSuccess: true)
              ..go(state.route);
          },
          loginError: (state) {
            Loader.hide();
            context.showToast(message: state.error, isSuccess: false);
          },
        );
      },
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.loginWithEmailPassword,
              style: AppStyles.mediumSemiBold.copyWith(
                color: context.colorScheme.onPrimaryFixed,
              ),
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: Strings.email,
              labelStyle: AppStyles.smallSemiBold.copyWith(
                color: AppColors.primaryLight,
              ),
              controller: emailTextController,
              validator: Validators().emailValidator,
              borderRadius: BorderRadius.circular(4),
              errorTextColor: context.colorScheme.onPrimaryFixed,
              outlineErrorBorder: true,
              showErrorPrefixIcon: true,
              errorContentTextColor: context.colorScheme.error,
              contextTextColor: context.colorScheme.onPrimaryFixed,
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: Strings.password,
              controller: passwordTextController,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Password is required';
                }
                return null;
              },
              borderRadius: BorderRadius.circular(4),
              labelStyle: AppStyles.smallSemiBold.copyWith(
                color: AppColors.primaryLight,
              ),
              keyboardType: TextInputType.visiblePassword,
              errorTextColor: context.colorScheme.onPrimaryFixed,
              outlineErrorBorder: true,
              showErrorPrefixIcon: true,
              contextTextColor: context.colorScheme.onPrimaryFixed,
            ),
            spacerH8,
            Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                borderRadius: BorderRadius.circular(4),
                onTap: () => context.push(PagePath.forgetPassword),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8),
                  child: Text(
                    Strings.forgetPassword,
                    style:
                        AppStyles.small.copyWith(color: AppColors.primaryLight),
                  ),
                ),
              ),
            ),
            // Align(
            //   alignment: Alignment.centerRight,
            //   child: TextButton(
            //     style: TextButton.styleFrom(
            //       splashFactory: InkRipple.splashFactory,
            //     ),
            //     onPressed: () => context.push(PagePath.forgetPassword),
            //     child: Text(
            //       Strings.forgetPassword,
            //       style:
            //           AppStyles.small.copyWith(color: AppColors.primaryLight),
            //     ),
            //   ),
            // ),
            const SizedBox(height: 12),
            GestureDetector(
              onTap: () => toggleRememberMe(!rememberMe),
              child: Row(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: Theme(
                      data: ThemeData(unselectedWidgetColor: Colors.white),
                      child: Checkbox(
                        side:
                            const BorderSide(color: AppColors.white, width: 2),
                        checkColor: AppColors.primary,
                        activeColor: AppColors.white,
                        value: rememberMe,
                        onChanged: toggleRememberMe,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    Strings.rememberMe,
                    style: AppStyles.mediumSemiBold
                        .copyWith(color: AppColors.white),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            CommonButton.basic(
              label: Strings.login,
              action: () {
                if (_formKey.currentState?.validate() ?? false) {
                  onLogin(context);
                }
              },
              backgroundColor: AppColors.white,
              textColor: AppColors.primary,
            ),
            CommonButton.basic(
              label: Strings.signUpInstead,
              action: () => authActionNotifier.value = AuthAction.signup,
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
            ),
          ],
        ),
      ),
    );
  }

  @override
  FutureOr<void> onBuilt(BuildContext context) {
    if (widget.localAuthEnabled) {
      context.read<LoginWithEmailCubit>().runBiometricAuth();
    }
  }
}
