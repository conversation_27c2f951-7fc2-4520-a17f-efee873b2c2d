defmodule BuyerboardBackendWeb.ImageController do
  use BuyerboardBackendWeb, :controller
  alias BuyerboardBackend.Images.Image
  alias BuyerboardBackend.Images
  alias BuyerboardBackend.FileUploader

  def create(conn, image_params) do
    case Images.create_image(image_params) do
      {:ok, %Image{} = image} ->
        conn
        |> put_status(:created)
        |> render("show.json", image: image)

      {:error, %Ecto.Changeset{} = changeset} ->
        error = translate_errors(changeset)

        conn
        |> put_status(:bad_request)
        |> render("error.json", error: error)

      _ ->
        conn
        |> put_status(:bad_request)
        |> render("error.json", error: "Bad Request")
    end
  end

  # New function for uploading a file and returning the URL without saving to DB

  def upload_attachment(conn, %{"files" => files}) do
    files = normalize_files(files)

    results =
      files
      |> Enum.map(&process_file/1)

    conn
    |> put_status(:ok)
    |> json(%{data: results, message: %{title: nil, body: "Files uploaded successfully"}})
  end

  defp normalize_files(%Plug.Upload{} = single_file), do: [single_file]
  defp normalize_files(files) when is_list(files), do: files
  defp normalize_files(_), do: []

  defp process_file(file) do
    modified_file = timestamp_file(file)

    case FileUploader.store({modified_file, %{}}) do
      {:ok, uploaded_file} ->
        build_upload_result(file, uploaded_file)

      {:error, reason} ->
        %{error: "Failed to upload file", reason: reason}
    end
  end

  defp timestamp_file(file) do
    timestamp = :os.system_time(:seconds)
    file_extension = Path.extname(file.filename)
    file_name_without_extension = Path.basename(file.filename, file_extension)
    timestamped_filename = "#{file_name_without_extension}_#{timestamp}#{file_extension}"

    %Plug.Upload{file | filename: timestamped_filename}
  end

  defp build_upload_result(file, uploaded_file) do
    url = FileUploader.url({uploaded_file, %{}})
    full_url = BuyerboardBackendWeb.Endpoint.url() <> url

    file_type = determine_file_type(file.content_type)
    thumbnail_url = generate_thumbnail(file_type, uploaded_file)

    %{
      url: full_url,
      type: file_type,
      thumbnail_url: thumbnail_url
    }
    |> Map.reject(fn {_k, v} -> is_nil(v) end)
  end

  defp determine_file_type("image/" <> _), do: "image"
  defp determine_file_type("video/" <> _), do: "video"
  defp determine_file_type(_), do: "other"

  defp generate_thumbnail("video", uploaded_file), do: generate_video_thumbnail(uploaded_file)
  defp generate_thumbnail(_, _), do: nil

  def generate_video_thumbnail(file_path) do
    # Determine the platform (Windows or Ubuntu)
    os_type = :os.type()

    ffmpeg_path = case os_type do
      {:win32, _} -> "C:/ffmpeg/bin/ffmpeg.exe"  # Absolute path for Windows
      _ -> "ffmpeg"  # Ubuntu's default (ensure ffmpeg is installed in your PATH)
    end

    # Ensure the paths are correctly constructed
    video_file = Path.expand("uploads/#{file_path}")
    thumbnail_file = Path.join(Path.dirname(video_file), "#{Path.basename(video_file, Path.extname(video_file))}_thumbnail.jpg")


    # Run ffmpeg to generate the thumbnail
    {output, exit_status} = System.cmd(ffmpeg_path, [
      "-i", video_file,
      "-ss", "00:00:01",        # Capture the thumbnail at 1 second
      "-vframes", "1",          # Capture only one frame (the thumbnail)
      "-update", "1",           # Ensure we update the output file if it already exists
      thumbnail_file            # Output file for the thumbnail
    ], [])

    # Log the output and exit status for debugging


    # Check if ffmpeg command failed
    if exit_status != 0 do
      IO.puts("FFMPEG command failed. Output: #{output}")
      {:error, "Failed to generate thumbnail"}  # Return an error tuple
    else

      # Get the base URL from config (this will be dynamic based on the environment)
      base_url = BuyerboardBackendWeb.Endpoint.url()

      # Return the URL of the generated thumbnail in a map
      thumbnail_url = "#{base_url}/uploads/#{Path.basename(thumbnail_file)}"
      thumbnail_url  # Return just the URL string
    end
    end

  def delete_uploaded_attachments(conn, %{"data" => files}) do
    results =
      Enum.map(files, fn file ->
        case delete_file(file) do
          :ok ->
            %{status: "success", type: file["type"], url: file["url"]}

          {:error, reason} ->
            %{status: "error", type: file["type"], url: file["url"], error: reason}
        end
      end)

    conn
    |> put_status(:ok)
    |> json(%{data: results, message: %{title: nil, body: "Files processed for deletion"}})
  end

  # Helper function to delete the file
  defp delete_file(%{"url" => url, "type" => type} = file) do
    # Extract the file path from the URL
    base_url = BuyerboardBackendWeb.Endpoint.url()
    file_path = String.replace(url, base_url <> "/", "")

    # Handle thumbnail deletion for videos
    if type == "video" and Map.has_key?(file, "thumbnail_url") do
      thumbnail_url = file["thumbnail_url"]
      thumbnail_path = String.replace(thumbnail_url, base_url <> "/", "")

      # Delete the thumbnail
      case File.rm(thumbnail_path) do
        :ok -> :ok
        {:error, reason} -> IO.puts("Failed to delete thumbnail: #{reason}")
      end
    end

    # Delete the main file
    case File.rm(file_path) do
      :ok -> :ok
      {:error, reason} -> {:error, reason}
    end
  end

  defp translate_errors(user) do
    Ecto.Changeset.traverse_errors(user, &BuyerboardBackendWeb.ErrorHelpers.translate_error/1)
  end
end
