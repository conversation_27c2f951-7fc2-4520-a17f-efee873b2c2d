import 'package:flutter/material.dart';

abstract final class ScreenUtils {
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.sizeOf(context).height;
  }

  static double bottomPadding(BuildContext context) {
    return MediaQuery.paddingOf(context).bottom;
  }

  static double topPadding(BuildContext context) {
    return MediaQuery.paddingOf(context).top;
  }

  static double getScreenWidth(BuildContext context) {
    return MediaQuery.sizeOf(context).width;
  }
}
