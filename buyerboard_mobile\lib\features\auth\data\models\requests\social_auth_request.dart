import 'package:json_annotation/json_annotation.dart';
part 'social_auth_request.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
)
class SocialAuthRequest {
  SocialAuthRequest({
    required this.email,
    required this.provider,
    this.firstName,
    this.lastName,
  });

  final String email;
  final String provider;
  final String? firstName;
  final String? lastName;

  Map<String, dynamic> toJson() => _$SocialAuthRequestToJson(this);
}
